import { createSelector } from '@reduxjs/toolkit';
import { RootState } from './../store';
import { getTaxCalculationInfo } from '../../utils/cart';
import { getTaxCalculationInfoNew } from '../../utils/cart-offline';

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
const selectCartState = (state: RootState) => state.cart;
const selectCartOfflineState = (state: RootState) => state.cartOffline;
const selectCustomerState = (state: RootState) => state.customer;

export const getCartProducts = createSelector(
	[selectCartState],
	(cart) => {
		if (cart.cartSearchKey) {
			return cart.cartSearchedProducts;
		}
		return cart.cartProducts;
	}
);

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
export const getCartProductsOffline = createSelector(
	[selectCartOfflineState, selectCustomerState],
	(cartOffline, customer) => {
		const activeCustomer = customer.activeCustomer;
		if (activeCustomer) {
			return cartOffline.cartItems;
		}
		return [];
	}
);

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
export const getCartProductsCount = createSelector(
	[selectCartOfflineState, selectCustomerState],
	(cartOffline, customer) => {
		const activeCustomer = customer.activeCustomer;
		if (activeCustomer && Array.isArray(cartOffline?.cartItems)) {
			return cartOffline.cartItems?.length;
		}
		return 0;
	}
);

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
export const getCartProductsWithSearch = createSelector(
	[selectCartState, selectCartOfflineState, selectCustomerState],
	(cart, cartOffline, customer) => {
		const activeCustomer = customer.activeCustomer;
		if (activeCustomer) {
			if (cart.cartSearchKey) {
				return cart.cartSearchedProducts;
			}
			return cartOffline.cartItems;
		}
		return [];
	}
);

export const getCartCount = createSelector(
	[selectCartOfflineState, selectCustomerState],
	(cartOffline, customer) => {
		const activeCustomer = customer.activeCustomer;
		if (activeCustomer) {
			return cartOffline.cartItems.length;
		}
		return 0;
	}
);

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
export const getCartShippingDetails = createSelector(
	[selectCustomerState],
	(customer) => {
		const activeCustomer = customer.activeCustomer;
		if (
			activeCustomer &&
			activeCustomer.gps_coordinates &&
			activeCustomer.gps_coordinates.latitude !== 0 &&
			activeCustomer.gps_coordinates.longitude !== 0
		) {
			return {
				city: activeCustomer.shipping_city_id?.name,
				shippingCityId: activeCustomer.shipping_city_id?._id,
				country: activeCustomer.shipping_country_id?.name,
				latitude: activeCustomer.gps_coordinates.latitude,
				longitude: activeCustomer.gps_coordinates.longitude,
				region: activeCustomer.shipping_region_id?.name,
				shippingRegionId: activeCustomer.shipping_region_id?._id,
				shippingAddress: activeCustomer.shipping_address,
				shippingCountryCode: activeCustomer.shipping_country_code,
				shippingMobileNumber: activeCustomer.shipping_mobile_number
			};
		}
		return null;
	}
);

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
export const getCartProductsInfo = createSelector(
	[selectCartOfflineState, selectCustomerState],
	(cartOffline, customer) => {
		const activeCustomer = customer.activeCustomer;
		if (activeCustomer) {
			return getTaxCalculationInfo(cartOffline.cartItems);
		}
		return {};
	}
);

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
export const getCartTaxData = createSelector(
	[selectCartOfflineState, selectCustomerState],
	(cartOffline, customer) => {
		const activeCustomer = customer.activeCustomer;
		if (activeCustomer) {
			return getTaxCalculationInfoNew(cartOffline.cartItems);
		}
		return [];
	}
);

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
export const getProductCartDataOffline = createSelector(
	[selectCartOfflineState, selectCustomerState, (state: RootState, productId: any) => productId],
	(cartOffline, customer, productId) => {
		const activeCustomer = customer.activeCustomer;
		if (activeCustomer) {
			const cartItems = cartOffline.cartItems;
			if (cartItems.length && productId) {
				const addedInCart = cartItems?.find((x: any) => x.product_variant_id === productId);
				return {
					cartItemId: addedInCart ? addedInCart._id : undefined,
					quantity: addedInCart && addedInCart.quantity ? addedInCart.quantity : 0
				};
			}
			return { cartItemId: undefined, quantity: 0 };
		}
		return { cartItemId: undefined, quantity: 0 };
	}
);

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
export const getCartInfoByProductVariantId = createSelector(
	[selectCartOfflineState, (state: RootState, product_variant_id: any) => product_variant_id],
	(cartOffline, product_variant_id) => {
		if (product_variant_id && cartOffline?.cartItems && cartOffline.cartItems?.length > 0) {
			const CartInfoById = cartOffline.cartItems.filter(
				(item) => item.product_variant_id === product_variant_id
			);
			if (CartInfoById && CartInfoById.length > 0) {
				return CartInfoById[0];
			}
		}
		return undefined;
	}
);

/* -------------------------------------------------------------------------- */
/*                           // Only used in offline                          */
/* -------------------------------------------------------------------------- */
export const getCartId = createSelector(
	[selectCustomerState, (state: RootState) => state.auth],
	(customer, auth) => {
		const activeCustomer = customer.activeCustomer;
		if (activeCustomer) {
			const tenantId = auth.currentRole?.tenant_id?._id;
			const userRoleId = auth.currentRole._id;
			const customerId = activeCustomer._id;
			return `${tenantId}_${customerId}_${userRoleId}`;
		}
		return '';
	}
);
