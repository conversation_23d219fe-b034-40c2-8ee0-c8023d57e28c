import React, { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import Modal from 'react-native-modal';
import MapView, { PROVIDER_GOOGLE, Region } from 'react-native-maps';
import {
	<PERSON>ert,
	I18nManager,
	InteractionManager,
	LayoutChangeEvent,
	Platform,
	StatusBar,
	StyleSheet,
	TextInput,
	TouchableOpacity,
	View
} from 'react-native';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import LocationInformation from './LocationInformation';
import ShippingNumber from './ShippingNumber';
import PlacesSearchResults from './PlacesSearchResults';
import {
	ArrowLeft,
	CloseCircle,
	LocationGPS,
	LocationPin,
	Search
} from '../../../assets/svgs/icons';
import {
	HORIZONTAL_DIMENS,
	SEARCH_TYPES,
	VERTICAL_DIMENS
} from '../../../constants';
import {
	getTenantCities,
	placesAutoComplete
} from '../../../redux/apis/common';
import { useAppSelector } from '../../../redux/hooks';
import { colors, fonts, headerHeightMobile } from '../../../utils/theme';
import { updateCustomerTenantShippingAddress } from '../../../redux/apis/customer';
import {
	Geocoder,
	checkBadRequest,
	getCurrentLocation
} from '../../../utils/functions';
import { getTenantCountry } from '../../../redux/selectors';
import { addCity } from '../../../redux/apis/common';
import useStatusBarHeight from '../../../hooks/useStatusbarHeight';

type Props = {
	isVisible: boolean;
	onClose: () => void;
	onSelect: (data: { [key: string]: string | number }) => void;
	customerRoleId?: string;
	shippingNumber?: string | undefined;
};

const SelectLocation = ({
	isVisible,
	onClose,
	onSelect,
	customerRoleId,
	shippingNumber
}: Props) => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const mapRef = useRef<MapView | null>(null);
	const { bottom } = useSafeAreaInsets();
	const tenantCountry = useAppSelector(getTenantCountry);
	const currentRole = useAppSelector((state) => state.auth.currentRole);
	const countryId = currentRole?.tenant_id?.country?._id;
	const countryName = currentRole?.tenant_id?.country?.name;

	const [height, setHeight] = useState<any>(0);
	const [step, setStep] = useState<string>('1');
	const [searchKey, setSearchKey] = useState<string>('');
	const [savingLocation, setSavingLocation] = useState(false);
	const [loading, setLoading] = useState(false);
	const [isInitialLocationSet, setIsInitialLocationSet] = useState(false);
	const [region, setRegion] = useState<Region | null>(null);
	const [addressDetails, setAddressDetails] = useState({
		city: '',
		shippingCityId: '',
		region: '',
		shippingRegionId: '',
		country: '',
		shippingAddress: '',
		regionCode: ''
	});

	// Only geocode when we have a valid region and initial location is set
	useEffect(() => {
		// Don't geocode if region is null or initial location hasn't been set
		if (!region || !isInitialLocationSet) {
			return;
		}

		const timeoutId = setTimeout(() => {
			Geocoder.from(region.latitude, region.longitude)
				.then((json: any) => {
					const result = json.results[0];
					const address = Geocoder.getAddressComponents(result);
					setAddressDetails(address);
					setLoading(false);
				})
				.catch((error: any) => {
					setLoading(false);
					console.warn(error);
				});
		}, 500); // Debounce geocoding calls

		return () => clearTimeout(timeoutId);
	}, [region, isInitialLocationSet]);

	useEffect(() => {
		dispatch(placesAutoComplete(searchKey));
	}, [searchKey]);

	// Reset state when modal visibility changes
	useEffect(() => {
		if (!isVisible) {
			// Reset to initial state when modal is closed
			setIsInitialLocationSet(false);
			setStep('1');
			setSearchKey('');
			setLoading(false);
			setRegion(null);
			setAddressDetails({
				city: '',
				shippingCityId: '',
				region: '',
				shippingRegionId: '',
				country: '',
				shippingAddress: '',
				regionCode: ''
			});
		}
	}, [isVisible]);

	const dynamicContentLayoutHandle = (event: LayoutChangeEvent) => {
		// Sets the "new height" every time this function is rendered
		step === '1' && setHeight(event.nativeEvent.layout.height + 24);
	};

	/* Get user location and set region for initial map load */
	const getUserLocation = async () => {
		const location: any = await getCurrentLocation();
		if (location !== null) {
			const { coords } = location;
			const newRegion: Region = {
				latitude: coords?.latitude,
				longitude: coords?.longitude,
				latitudeDelta: 0.015,
				longitudeDelta: 0.0121
			};
			setIsInitialLocationSet(true);
			setRegion(newRegion);
			mapRef.current?.animateToRegion(newRegion);
		}
	};

	const onRegionChange = (newRegion: Region) => {
		setRegion(newRegion);
		// Ensure we mark that initial location has been set if user interacts with map
		if (!isInitialLocationSet) {
			setIsInitialLocationSet(true);
		}
	};

	const onGoBack = () => {
		step === '2' ? setStep('1') : onClose();
	};

	const getCities = () => {
		const selectedCity = addressDetails.city.toLowerCase();
		return new Promise<Array<any>>(async (resolve) => {
			try {
				const params = {
					type: SEARCH_TYPES.SEARCH,
					searchKey: selectedCity
				};
				const response = await dispatch(getTenantCities(params));
				if (!response.error) {
					resolve(response.payload.data);
					return;
				}
				resolve([]);
			} catch {
				resolve([]);
			}
		});
	};

	const onNext = async () => {
		const selectedCity = addressDetails.city.toLowerCase();
		setLoading(true);
		const cities = await getCities();
		const matchingCity = cities.find((x) => {
			const englishMactch = selectedCity === x.name.toLowerCase();
			const secondaryMatch =
				selectedCity === x.secondary_language_name?.toLowerCase();
			return englishMactch || secondaryMatch;
		});
		if (matchingCity) {
			if (countryId && matchingCity?.country_id?._id === countryId) {
				setLoading(false);
				setAddressDetails({
					...addressDetails,
					shippingCityId: matchingCity._id,
					shippingRegionId: matchingCity.region_id?._id
				});
				setStep('2');
			} else {
				setLoading(false);
				Alert.alert(`${t('select_location_within')} ${countryName}.`);
			}
		} else {
			const requestBody: any = {
				cityName: addressDetails.city,
				countryName: addressDetails.country,
				regionName: addressDetails.region
			};
			if (addressDetails.regionCode) {
				requestBody.regionCode = addressDetails.regionCode;
			}
			try {
				const response = await dispatch(addCity(requestBody));
				setLoading(false);
				if (response.error) {
					// console.log("🚀 ~ onNext ~ response.error:", JSON.stringify(response.error))
					Alert.alert(t(response.payload.message));
				} else {
					dispatch(getTenantCities()); // Update cities list
					setAddressDetails({
						...addressDetails,
						shippingCityId: response.payload.data._id,
						shippingRegionId: response.payload.data.region_id
					});
					setStep('2');
				}
			} catch (error) {
				Alert.alert('AddCityError', `${JSON.stringify(error)}`);
			}
		}
	};

	/* Pass selected location, address details and mobile number to add customer screen */
	const onSubmitNumber = async (
		shippingMobileNumber: string,
		shippingCountryCode: string
	) => {
		//If leading zero then remove
		let number = shippingMobileNumber;
		const result = number.startsWith('0');
		result === true ? (number = number.substring(1)) : number;

		if (customerRoleId) {
			// Customer role id available so save location in database then close modal
			setSavingLocation(true);
			const requestBody = {
				ids: [customerRoleId],
				updateFields: {
					shipping_address: addressDetails.shippingAddress,
					shipping_country_id: tenantCountry._id,
					shipping_city_id: addressDetails.shippingCityId,
					shipping_region_id: addressDetails.shippingRegionId,
					shipping_country_code: shippingCountryCode,
					shipping_mobile_number: number,
					gps_coordinates: {
						latitude: region?.latitude,
						longitude: region?.longitude
					}
				}
			};
			const response = await dispatch(
				updateCustomerTenantShippingAddress(requestBody)
			);
			setSavingLocation(false);
			if (!response.error) {
				onSelect({
					...addressDetails,
					...region,
					shippingMobileNumber,
					shippingCountryCode
				});
				InteractionManager.runAfterInteractions(() => {
					setStep('1'); // Start from step one if user open select location again
				});
			} else {
				Alert.alert(t(checkBadRequest(response.payload)));
			}
			return;
		}

		// if (customerRoleId && userType === USER_TYPES.CUSTOMER_APP) {
		// 	// Customer role id available so save location in database then close modal
		// 	setSavingLocation(true);
		// 	const requestBody = {
		// 		shipping_address: addressDetails.shippingAddress,
		// 		shipping_country_id: tenantCountry._id,
		// 		shipping_city_id: addressDetails.shippingCityId,
		// 		shipping_region_id: addressDetails.shippingRegionId,
		// 		shipping_country_code: shippingCountryCode,
		// 		shipping_mobile_number: number,
		// 		gps_coordinates: {
		// 			latitude: region?.latitude,
		// 			longitude: region?.longitude
		// 		}
		// 	};
		// 	const response = await dispatch(updateCustomerShippingAddress(requestBody));
		// 	setSavingLocation(false);
		// 	if (!response.error) {
		// 		onSelect({
		// 			...addressDetails,
		// 			...region,
		// 			shippingMobileNumber,
		// 			shippingCountryCode
		// 		});
		// 		InteractionManager.runAfterInteractions(() => {
		// 			setStep('1'); // Start from step one if user open select location again
		// 		});
		// 	} else {
		// 		Alert.alert(t(checkBadRequest(response.payload)));
		// 	}
		// 	return;
		// }

		onSelect({
			...addressDetails,
			...region,
			shippingMobileNumber,
			shippingCountryCode
		});
		InteractionManager.runAfterInteractions(() => {
			setStep('1'); // Start from step one if user open select location again
		});
	};

	const onUserLocation = async () => {
		const location: any = await getCurrentLocation();
		if (location !== null) {
			const { coords } = location;
			const newRegion: Region = {
				latitude: coords?.latitude,
				longitude: coords?.longitude,
				latitudeDelta: 0.015,
				longitudeDelta: 0.0121
			};
			setIsInitialLocationSet(true);
			setRegion(newRegion);
			mapRef.current?.animateToRegion(newRegion);
		}
	};

	const onSelectPlace = (location: any) => {
		setSearchKey('');
		const newRegion: Region = {
			latitude: location.lat,
			longitude: location.lng,
			latitudeDelta: 0.015,
			longitudeDelta: 0.0121
		};
		setIsInitialLocationSet(true);
		setRegion(newRegion);
		mapRef.current?.animateToRegion(newRegion);
	};

	const statusBarHeight = useStatusBarHeight();
	return (
		<Modal
			isVisible={isVisible}
			style={styles.modalContainer}
			onModalShow={getUserLocation}
		>
			<View style={styles.modalContent}>
				<StatusBar
					barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'}
				/>
				<MapView
					provider={PROVIDER_GOOGLE}
					ref={mapRef}
					style={styles.map}
					initialRegion={region || undefined}
					rotateEnabled={false}
					pitchEnabled={false}
					showsUserLocation={true}
					showsMyLocationButton={false}
					showsIndoors={false}
					onRegionChangeComplete={onRegionChange}
					onRegionChangeStart={() => {
						setLoading(true);
					}}
					mapPadding={{ top: 0, right: 0, bottom: height - 22, left: 0 }}
					scrollEnabled={step === '1'}
					zoomControlEnabled={step === '1'}
					zoomEnabled={step === '1'}
					zoomTapEnabled={step === '1'}
					scrollDuringRotateOrZoomEnabled={step === '1'}
				/>
				<View
					style={[styles.markerFixed, { bottom: height }]}
					pointerEvents="none"
				>
					<LocationPin fill={colors.primary} />
				</View>
				{step === '1' && (
					<TouchableOpacity
						style={[styles.locationBtn, { bottom: height + 16 }]}
						onPress={onUserLocation}
					>
						<LocationGPS fill={colors.primary} />
					</TouchableOpacity>
				)}
				<View
					style={[
						styles.modalHeader,
						Platform.OS === 'ios' && { top: statusBarHeight }
					]}
				>
					<TouchableOpacity onPress={onGoBack} disabled={savingLocation}>
						<ArrowLeft
							fill={colors.primary}
							style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }}
						/>
					</TouchableOpacity>
					<View style={styles.searchInputContainer}>
						<TextInput
							style={styles.searchInput}
							placeholder={t('search_place')}
							placeholderTextColor={colors.grey400}
							value={searchKey}
							onChangeText={setSearchKey}
							editable={step === '1'}
						// onFocus={() => setShowPlaces(true)}
						// onBlur={() => setShowPlaces(false)}
						/>
						<TouchableOpacity
							style={styles.clearSearchBtn}
							onPress={() => setSearchKey('')}
							disabled={!searchKey}
						>
							<CloseCircle fill={colors.grey600} />
						</TouchableOpacity>
						<View style={styles.searchIconContainer}>
							<Search fill={colors.grey600} />
						</View>
						<PlacesSearchResults onSelect={onSelectPlace} />
					</View>
				</View>
				<BottomSheet
					index={0}
					animateOnMount={true}
					handleIndicatorStyle={styles.bottomSheetIndicator}
					keyboardBlurBehavior="restore"
					enableDynamicSizing={true}
				>
					<BottomSheetView
						onLayout={dynamicContentLayoutHandle}
						style={[
							styles.bottomSheet,
							bottom > 0 && { paddingBottom: bottom },
							Platform.OS === 'android' && {
								paddingBottom: VERTICAL_DIMENS._20
							}
						]}
					>
						{step === '1' && (
							<LocationInformation
								addressDetails={addressDetails}
								onSubmit={onNext}
								loading={loading}
							/>
						)}
						{step === '2' && (
							<ShippingNumber
								loading={savingLocation}
								onSubmit={onSubmitNumber}
								shippingNumber={shippingNumber}
							/>
						)}
					</BottomSheetView>
				</BottomSheet>
			</View>
		</Modal>
	);
};

const styles = StyleSheet.create({
	modalContainer: {
		justifyContent: 'flex-end',
		margin: 0
	},
	modalContent: {
		backgroundColor: colors.grey100,
		flex: 1
	},
	modalHeader: {
		alignItems: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		flexDirection: 'row',
		width: '100%',
		height: headerHeightMobile,
		position: 'absolute'
	},
	searchInputContainer: {
		flex: 1,
		marginLeft: HORIZONTAL_DIMENS._14
	},
	searchInput: {
		backgroundColor: colors.white,
		borderRadius: 20,
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._14,
		height: 40,
		justifyContent: 'center',
		paddingLeft: HORIZONTAL_DIMENS._48,
		textAlign: I18nManager.isRTL ? 'right' : 'left',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	searchIconContainer: {
		position: 'absolute',
		height: 40,
		justifyContent: 'center',
		left: HORIZONTAL_DIMENS._16
	},
	clearSearchBtn: {
		position: 'absolute',
		height: 40,
		justifyContent: 'center',
		paddingRight: HORIZONTAL_DIMENS._10,
		right: 0
	},
	map: {
		flex: 1
	},
	markerFixed: {
		position: 'absolute',
		top: 0,
		bottom: 22,
		left: 0,
		right: 0,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'transparent'
	},
	bottomSheet: {
		paddingHorizontal: HORIZONTAL_DIMENS._16
	},
	bottomSheetIndicator: {
		backgroundColor: colors.grey300
	},
	locationBtn: {
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: 30,
		justifyContent: 'center',
		position: 'absolute',
		//bottom: '42%',
		right: 16,
		height: 54,
		width: 54,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 0
		},
		shadowOpacity: 0.14,
		shadowRadius: 2.62,
		elevation: 3
	}
});

export { SelectLocation };
