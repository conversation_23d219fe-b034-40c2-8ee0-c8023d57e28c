import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import Modal from 'react-native-modal';
import MapView, { PROVIDER_GOOGLE, Region } from 'react-native-maps';
import {
	Alert,
	I18nManager,
	InteractionManager,
	LayoutChangeEvent,
	Platform,
	StatusBar,
	StyleSheet,
	TextInput,
	TouchableOpacity,
	View
} from 'react-native';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import LocationInformation from './LocationInformation';
import ShippingNumber from './ShippingNumber';
import PlacesSearchResults from './PlacesSearchResults';
import {
	ArrowLeft,
	CloseCircle,
	LocationGPS,
	LocationPin,
	Search
} from '../../../assets/svgs/icons';
import {
	HORIZONTAL_DIMENS,
	SEARCH_TYPES,
	VERTICAL_DIMENS
} from '../../../constants';
import {
	getTenantCities,
	placesAutoComplete
} from '../../../redux/apis/common';
import { useAppSelector } from '../../../redux/hooks';
import { colors, fonts, headerHeightMobile } from '../../../utils/theme';
import { updateCustomerTenantShippingAddress } from '../../../redux/apis/customer';
import {
	Geocoder,
	checkBadRequest,
	getCurrentLocation
} from '../../../utils/functions';
import Config from 'react-native-config';
import { getTenantCountry } from '../../../redux/selectors';
import { addCity } from '../../../redux/apis/common';
import useStatusBarHeight from '../../../hooks/useStatusbarHeight';

// ============================================================================
// CONSTANTS & TYPES
// ============================================================================

const MAP_DELTAS = {
	// More zoomed out for better location context
	STREET_LEVEL: {
		latitudeDelta: 0.008,
		longitudeDelta: 0.008
	},
	// Even more zoomed out for initial view
	NEIGHBORHOOD_LEVEL: {
		latitudeDelta: 0.02,
		longitudeDelta: 0.02
	}
} as const;

const GEOCODING_DEBOUNCE_MS = 800; // Increased debounce for better performance
const LOCATION_TIMEOUT_MS = 20000; // Increased timeout for better accuracy

interface AddressDetails {
	city: string;
	shippingCityId: string;
	region: string;
	shippingRegionId: string;
	country: string;
	shippingAddress: string;
	regionCode: string;
}

// ============================================================================
// COMPONENT PROPS & TYPES
// ============================================================================

type Props = {
	isVisible: boolean;
	onClose: () => void;
	onSelect: (data: { [key: string]: string | number }) => void;
	customerRoleId?: string;
	shippingNumber?: string | undefined;
	// Pre-existing location data
	initialLocation?: {
		latitude?: number;
		longitude?: number;
		address?: string;
		city?: string;
		region?: string;
		country?: string;
	};
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

const SelectLocation = ({
	isVisible,
	onClose,
	onSelect,
	customerRoleId,
	shippingNumber,
	initialLocation
}: Props) => {
	// ========================================================================
	// HOOKS & SELECTORS
	// ========================================================================
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const mapRef = useRef<MapView | null>(null);
	const { bottom } = useSafeAreaInsets();
	const statusBarHeight = useStatusBarHeight();

	// Redux selectors
	const tenantCountry = useAppSelector(getTenantCountry);
	const currentRole = useAppSelector((state) => state.auth.currentRole);
	const countryId = currentRole?.tenant_id?.country?._id;
	const countryName = currentRole?.tenant_id?.country?.name;

	// ========================================================================
	// STATE MANAGEMENT
	// ========================================================================
	const [height, setHeight] = useState<number>(0);
	const [step, setStep] = useState<'1' | '2'>('1');
	const [searchKey, setSearchKey] = useState<string>('');
	const [savingLocation, setSavingLocation] = useState(false);
	const [loading, setLoading] = useState(false);
	const [isInitialLocationSet, setIsInitialLocationSet] = useState(false);
	const [region, setRegion] = useState<Region | null>(null);
	const [addressDetails, setAddressDetails] = useState<AddressDetails>({
		city: '',
		shippingCityId: '',
		region: '',
		shippingRegionId: '',
		country: '',
		shippingAddress: '',
		regionCode: ''
	});

	// ========================================================================
	// EFFECTS
	// ========================================================================

	// Geocoding effect - only runs when we have valid region and location is set
	useEffect(() => {
		if (!region || !isInitialLocationSet) {
			return;
		}

		// Skip geocoding if we already have initial address details from props
		if (initialLocation?.address && addressDetails.shippingAddress) {
			console.log('Skipping geocoding - using provided initial address:', initialLocation.address);
			return;
		}

		// Validate coordinates before geocoding
		const { latitude, longitude } = region;
		if (!isValidCoordinate(latitude, longitude)) {
			console.warn('Invalid coordinates for geocoding:', { latitude, longitude });
			return;
		}

		const timeoutId = setTimeout(() => {
			setLoading(true);

			// Check if Geocoder is initialized
			if (!Geocoder.isInit) {
				console.error('Geocoder not initialized');
				setLoading(false);
				return;
			}

			console.log('Starting geocoding for coordinates:', { latitude, longitude });

			Geocoder.from(latitude, longitude)
				.then((json: any) => {
					console.log('Geocoding response:', json);

					// Check if we have results
					if (!json.results || json.results.length === 0) {
						console.warn('No geocoding results found for coordinates:', { latitude, longitude });
						setAddressDetails({
							city: '',
							shippingCityId: '',
							region: '',
							shippingRegionId: '',
							country: '',
							shippingAddress: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`, // Fallback to coordinates
							regionCode: ''
						});
						return;
					}

					const result = json.results[0];
					console.log('Using geocoding result:', result);
					const address = Geocoder.getAddressComponents(result);
					console.log('Parsed address:', address);
					setAddressDetails(address);
				})
				.catch((error: any) => {
					console.warn('Geocoding error:', error);

					// Handle specific error cases
					if (error.code === 4 && error.origin?.status === 'ZERO_RESULTS') {
						// No results found - set fallback address
						setAddressDetails({
							city: '',
							shippingCityId: '',
							region: '',
							shippingRegionId: '',
							country: '',
							shippingAddress: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
							regionCode: ''
						});
					} else if (error.code === 4 && error.origin?.status === 'OVER_QUERY_LIMIT') {
						Alert.alert(t('error'), t('geocoding_quota_exceeded'));
					} else {
						// For other errors, show a user-friendly message
						console.error('Geocoding failed:', error);
					}
				})
				.finally(() => {
					setLoading(false);
				});
		}, GEOCODING_DEBOUNCE_MS);

		return () => clearTimeout(timeoutId);
	}, [region, isInitialLocationSet, initialLocation?.address, addressDetails.shippingAddress, t]);

	// Places autocomplete effect
	useEffect(() => {
		if (searchKey.trim()) {
			dispatch(placesAutoComplete(searchKey));
		}
	}, [searchKey, dispatch]);



	// Initialize and validate Geocoder
	useEffect(() => {
		console.log('🔍 Geocoder Debug Information:');
		console.log('- Platform:', Platform.OS);
		console.log('- Config API Key iOS:', Config.GOOGLE_MAPS_API_KEY_IOS ? '✅ Set' : '❌ Missing');
		console.log('- Config API Key Android:', Config.GOOGLE_MAPS_API_KEY_ANDROID ? '✅ Set' : '❌ Missing');
		console.log('- Geocoder isInit:', Geocoder.isInit);
		console.log('- Geocoder apiKey exists:', !!Geocoder.apiKey);
		console.log('- Geocoder apiKey length:', Geocoder.apiKey?.length || 0);
		console.log('- Geocoder apiKey preview:', Geocoder.apiKey ? `${Geocoder.apiKey.substring(0, 10)}...` : 'None');

		if (!Geocoder.isInit) {
			console.error('❌ Geocoder not initialized - API key missing or invalid');
			Alert.alert(
				'Configuration Error',
				`Google Maps API key is not configured for ${Platform.OS}. Please check your .env file and ensure GOOGLE_MAPS_API_KEY_${Platform.OS.toUpperCase()} is set.`,
				[{ text: 'OK' }]
			);
		} else {
			console.log('✅ Geocoder initialized successfully');

			// Test geocoding with a known location (optional - remove in production)
			if (__DEV__) {
				console.log('🧪 Testing geocoding with known coordinates...');
				Geocoder.from(25.2048, 55.2708) // Dubai coordinates
					.then((result: any) => {
						console.log('✅ Test geocoding successful:', result.results?.[0]?.formatted_address);
					})
					.catch((error: any) => {
						console.error('❌ Test geocoding failed:', error);
					});
			}
		}
	}, []);

	// ========================================================================
	// UTILITY FUNCTIONS
	// ========================================================================

	const isValidCoordinate = useCallback((latitude: number, longitude: number): boolean => {
		return (
			typeof latitude === 'number' &&
			typeof longitude === 'number' &&
			!isNaN(latitude) &&
			!isNaN(longitude) &&
			latitude >= -90 &&
			latitude <= 90 &&
			longitude >= -180 &&
			longitude <= 180 &&
			!(latitude === 0 && longitude === 0) // Exclude null island
		);
	}, []);



	const resetComponentState = useCallback(() => {
		setIsInitialLocationSet(false);
		setStep('1');
		setSearchKey('');
		setLoading(false);
		setRegion(null);
		setAddressDetails({
			city: '',
			shippingCityId: '',
			region: '',
			shippingRegionId: '',
			country: '',
			shippingAddress: '',
			regionCode: ''
		});
	}, []);

	const createRegionFromCoords = useCallback((latitude: number, longitude: number, zoomLevel: 'street' | 'neighborhood' = 'street'): Region => {
		const deltas = zoomLevel === 'street' ? MAP_DELTAS.STREET_LEVEL : MAP_DELTAS.NEIGHBORHOOD_LEVEL;
		return {
			latitude,
			longitude,
			...deltas
		};
	}, []);

	const animateToRegion = useCallback((newRegion: Region) => {
		setRegion(newRegion);
		mapRef.current?.animateToRegion(newRegion, 1000); // Smooth animation
	}, []);

	// Handle modal visibility and initial location setup
	useEffect(() => {
		if (isVisible) {
			// Modal is opening - set up initial location if provided
			if (initialLocation?.latitude && initialLocation?.longitude) {
				console.log('Setting up initial location:', initialLocation);
				const newRegion = createRegionFromCoords(
					initialLocation.latitude,
					initialLocation.longitude,
					'street'
				);
				setIsInitialLocationSet(true);
				setRegion(newRegion);

				// Always set initial address details if provided - prioritize stored data
				const hasStoredAddress = initialLocation.address && initialLocation.address.trim() !== '';
				if (hasStoredAddress || initialLocation.city) {
					console.log('Using stored address data:', {
						address: initialLocation.address,
						city: initialLocation.city,
						region: initialLocation.region,
						country: initialLocation.country
					});

					setAddressDetails({
						city: initialLocation.city || '',
						shippingCityId: '',
						region: initialLocation.region || '',
						shippingRegionId: '',
						country: initialLocation.country || '',
						shippingAddress: initialLocation.address || `${initialLocation.latitude.toFixed(6)}, ${initialLocation.longitude.toFixed(6)}`,
						regionCode: ''
					});
				}
			}
		} else {
			// Modal is closing - reset state
			resetComponentState();
		}
	}, [isVisible, initialLocation, createRegionFromCoords, resetComponentState]);

	// ========================================================================
	// LOCATION HANDLERS
	// ========================================================================

	const getUserLocation = useCallback(async () => {
		try {
			setLoading(true);

			// Use improved location options for better accuracy
			const location: any = await getCurrentLocation({
				timeout: LOCATION_TIMEOUT_MS,
				maximumAge: 3000, // Fresh location data
				enableHighAccuracy: true,
				showErrorAlert: false // Handle errors manually
			});

			if (location?.coords) {
				const { coords } = location;
				const accuracy = coords.accuracy;

				// Log accuracy for debugging
				console.log(`Location accuracy: ${accuracy}m`);

				// Use appropriate zoom level based on accuracy
				const zoomLevel = accuracy > 100 ? 'neighborhood' : 'street';
				const newRegion = createRegionFromCoords(
					coords.latitude,
					coords.longitude,
					zoomLevel
				);

				setIsInitialLocationSet(true);
				animateToRegion(newRegion);

				// If geocoder is not available, set fallback address
				if (!Geocoder.isInit) {
					setAddressDetails({
						city: '',
						shippingCityId: '',
						region: '',
						shippingRegionId: '',
						country: '',
						shippingAddress: `${coords.latitude.toFixed(6)}, ${coords.longitude.toFixed(6)}`,
						regionCode: ''
					});
				}
			} else {
				Alert.alert(t('location_error'), t('failed_to_load_location'));
			}
		} catch (error) {
			console.warn('Error getting user location:', error);
			Alert.alert(t('location_error'), t('failed_to_load_location'));
		} finally {
			setLoading(false);
		}
	}, [createRegionFromCoords, animateToRegion, t]);

	// Only get user location on modal show if no initial location is provided
	const handleModalShow = useCallback(() => {
		if (!initialLocation?.latitude || !initialLocation?.longitude) {
			console.log('No initial location provided, getting current location...');
			getUserLocation();
		} else {
			console.log('Initial location provided, skipping current location fetch');
		}
	}, [initialLocation, getUserLocation]);

	const onUserLocation = useCallback(async () => {
		try {
			setLoading(true);

			const location: any = await getCurrentLocation({
				timeout: LOCATION_TIMEOUT_MS,
				maximumAge: 1000, // Very fresh location
				enableHighAccuracy: true,
				showErrorAlert: true
			});

			if (location?.coords) {
				const { coords } = location;
				const newRegion = createRegionFromCoords(coords.latitude, coords.longitude, 'street');

				setIsInitialLocationSet(true);
				animateToRegion(newRegion);
			}
		} catch (error) {
			console.warn('Error getting current location:', error);
		} finally {
			setLoading(false);
		}
	}, [createRegionFromCoords, animateToRegion, t]);

	const onSelectPlace = useCallback((location: any) => {
		setSearchKey('');
		const newRegion = createRegionFromCoords(location.lat, location.lng, 'street');
		setIsInitialLocationSet(true);
		animateToRegion(newRegion);
	}, [createRegionFromCoords, animateToRegion]);

	// ========================================================================
	// MAP HANDLERS
	// ========================================================================

	const onRegionChange = useCallback((newRegion: Region) => {
		setRegion(newRegion);
		// Mark initial location as set if user manually interacts with map
		if (!isInitialLocationSet) {
			setIsInitialLocationSet(true);
		}
	}, [isInitialLocationSet]);

	const dynamicContentLayoutHandle = useCallback((event: LayoutChangeEvent) => {
		if (step === '1') {
			setHeight(event.nativeEvent.layout.height + 24);
		}
	}, [step]);

	// ========================================================================
	// NAVIGATION HANDLERS
	// ========================================================================

	const onGoBack = useCallback(() => {
		if (step === '2') {
			setStep('1');
		} else {
			onClose();
		}
	}, [step, onClose]);

	const getCities = () => {
		const selectedCity = addressDetails.city.toLowerCase();
		return new Promise<Array<any>>(async (resolve) => {
			try {
				const params = {
					type: SEARCH_TYPES.SEARCH,
					searchKey: selectedCity
				};
				const response = await dispatch(getTenantCities(params));
				if (!response.error) {
					resolve(response.payload.data);
					return;
				}
				resolve([]);
			} catch {
				resolve([]);
			}
		});
	};

	const onNext = async () => {
		// Validate that we have required address details
		if (!addressDetails.city || addressDetails.city.trim() === '') {
			Alert.alert(t('error'), t('please_select_valid_location'));
			return;
		}

		console.log('🔄 onNext - Starting with address details:', {
			city: addressDetails.city,
			region: addressDetails.region,
			country: addressDetails.country,
			shippingAddress: addressDetails.shippingAddress
		});

		const selectedCity = addressDetails.city.toLowerCase();
		setLoading(true);

		try {
			console.log('🏙️ Getting cities for:', selectedCity);
			const cities = await getCities();
			console.log('🏙️ Found cities:', cities.length);
			const matchingCity = cities.find((x) => {
				const englishMactch = selectedCity === x.name.toLowerCase();
				const secondaryMatch =
					selectedCity === x.secondary_language_name?.toLowerCase();
				return englishMactch || secondaryMatch;
			});

			console.log('🔍 City matching result:', {
				selectedCity,
				matchingCity: matchingCity ? {
					name: matchingCity.name,
					id: matchingCity._id,
					countryId: matchingCity?.country_id?._id
				} : null,
				expectedCountryId: countryId
			});
			if (matchingCity) {
				if (countryId && matchingCity?.country_id?._id === countryId) {
					console.log('✅ City matched and country valid - proceeding to step 2');
					setAddressDetails({
						...addressDetails,
						shippingCityId: matchingCity._id,
						shippingRegionId: matchingCity.region_id?._id
					});
					setStep('2');
				} else {
					console.log('❌ City matched but wrong country');
					Alert.alert(`${t('select_location_within')} ${countryName}.`);
				}
			} else {
				console.log('🆕 No matching city found - attempting to add new city');
				const requestBody: any = {
					cityName: addressDetails.city,
					countryName: addressDetails.country,
					regionName: addressDetails.region
				};
				if (addressDetails.regionCode) {
					requestBody.regionCode = addressDetails.regionCode;
				}
				try {
					const response = await dispatch(addCity(requestBody));
					if (response.error) {
						// console.log("🚀 ~ onNext ~ response.error:", JSON.stringify(response.error))
						Alert.alert(t(response.payload.message));
					} else {
						dispatch(getTenantCities()); // Update cities list
						setAddressDetails({
							...addressDetails,
							shippingCityId: response.payload.data._id,
							shippingRegionId: response.payload.data.region_id
						});
						setStep('2');
					}
				} catch (error) {
					console.error('AddCity error:', error);
					Alert.alert(t('error'), t('failed_to_add_city'));
				}
			}
		} catch (getCitiesError) {
			console.error('GetCities error:', getCitiesError);
			Alert.alert(t('error'), t('failed_to_load_cities'));
		} finally {
			// Always reset loading state regardless of success or failure
			setLoading(false);
		}
	};

	/* Pass selected location, address details and mobile number to add customer screen */
	const onSubmitNumber = async (
		shippingMobileNumber: string,
		shippingCountryCode: string
	) => {
		//If leading zero then remove
		let number = shippingMobileNumber;
		const result = number.startsWith('0');
		result === true ? (number = number.substring(1)) : number;

		if (customerRoleId) {
			// Customer role id available so save location in database then close modal
			setSavingLocation(true);
			const requestBody = {
				ids: [customerRoleId],
				updateFields: {
					shipping_address: addressDetails.shippingAddress,
					shipping_country_id: tenantCountry._id,
					shipping_city_id: addressDetails.shippingCityId,
					shipping_region_id: addressDetails.shippingRegionId,
					shipping_country_code: shippingCountryCode,
					shipping_mobile_number: number,
					gps_coordinates: {
						latitude: region?.latitude,
						longitude: region?.longitude
					}
				}
			};
			const response = await dispatch(
				updateCustomerTenantShippingAddress(requestBody)
			);
			setSavingLocation(false);
			if (!response.error) {
				onSelect({
					...addressDetails,
					...region,
					shippingMobileNumber,
					shippingCountryCode
				});
				InteractionManager.runAfterInteractions(() => {
					setStep('1'); // Start from step one if user open select location again
				});
			} else {
				Alert.alert(t(checkBadRequest(response.payload)));
			}
			return;
		}

		// if (customerRoleId && userType === USER_TYPES.CUSTOMER_APP) {
		// 	// Customer role id available so save location in database then close modal
		// 	setSavingLocation(true);
		// 	const requestBody = {
		// 		shipping_address: addressDetails.shippingAddress,
		// 		shipping_country_id: tenantCountry._id,
		// 		shipping_city_id: addressDetails.shippingCityId,
		// 		shipping_region_id: addressDetails.shippingRegionId,
		// 		shipping_country_code: shippingCountryCode,
		// 		shipping_mobile_number: number,
		// 		gps_coordinates: {
		// 			latitude: region?.latitude,
		// 			longitude: region?.longitude
		// 		}
		// 	};
		// 	const response = await dispatch(updateCustomerShippingAddress(requestBody));
		// 	setSavingLocation(false);
		// 	if (!response.error) {
		// 		onSelect({
		// 			...addressDetails,
		// 			...region,
		// 			shippingMobileNumber,
		// 			shippingCountryCode
		// 		});
		// 		InteractionManager.runAfterInteractions(() => {
		// 			setStep('1'); // Start from step one if user open select location again
		// 		});
		// 	} else {
		// 		Alert.alert(t(checkBadRequest(response.payload)));
		// 	}
		// 	return;
		// }

		onSelect({
			...addressDetails,
			...region,
			shippingMobileNumber,
			shippingCountryCode
		});
		InteractionManager.runAfterInteractions(() => {
			setStep('1'); // Start from step one if user open select location again
		});
	};

	// ========================================================================
	// RENDER
	// ========================================================================

	return (
		<Modal
			isVisible={isVisible}
			style={styles.modalContainer}
			onModalShow={handleModalShow}
		>
			<View style={styles.modalContent}>
				<StatusBar
					barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'}
				/>
				<MapView
					provider={PROVIDER_GOOGLE}
					ref={mapRef}
					style={styles.map}
					initialRegion={region || undefined}
					rotateEnabled={false}
					pitchEnabled={false}
					showsUserLocation={true}
					showsMyLocationButton={false}
					showsIndoors={false}
					onRegionChangeComplete={onRegionChange}
					onRegionChangeStart={() => {
						setLoading(true);
					}}
					mapPadding={{ top: 0, right: 0, bottom: height - 22, left: 0 }}
					scrollEnabled={step === '1'}
					zoomControlEnabled={step === '1'}
					zoomEnabled={step === '1'}
					zoomTapEnabled={step === '1'}
					scrollDuringRotateOrZoomEnabled={step === '1'}
				/>
				<View
					style={[styles.markerFixed, { bottom: height }]}
					pointerEvents="none"
				>
					<LocationPin fill={colors.primary} />
				</View>
				{step === '1' && (
					<TouchableOpacity
						style={[styles.locationBtn, { bottom: height + 16 }]}
						onPress={onUserLocation}
					>
						<LocationGPS fill={colors.primary} />
					</TouchableOpacity>
				)}
				<View
					style={[
						styles.modalHeader,
						Platform.OS === 'ios' && { top: statusBarHeight }
					]}
				>
					<TouchableOpacity onPress={onGoBack} disabled={savingLocation}>
						<ArrowLeft
							fill={colors.primary}
							style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }}
						/>
					</TouchableOpacity>
					<View style={styles.searchInputContainer}>
						<TextInput
							style={styles.searchInput}
							placeholder={t('search_place')}
							placeholderTextColor={colors.grey400}
							value={searchKey}
							onChangeText={setSearchKey}
							editable={step === '1'}
						// onFocus={() => setShowPlaces(true)}
						// onBlur={() => setShowPlaces(false)}
						/>
						<TouchableOpacity
							style={styles.clearSearchBtn}
							onPress={() => setSearchKey('')}
							disabled={!searchKey}
						>
							<CloseCircle fill={colors.grey600} />
						</TouchableOpacity>
						<View style={styles.searchIconContainer}>
							<Search fill={colors.grey600} />
						</View>
						<PlacesSearchResults onSelect={onSelectPlace} />
					</View>
				</View>
				<BottomSheet
					index={0}
					animateOnMount={true}
					handleIndicatorStyle={styles.bottomSheetIndicator}
					keyboardBlurBehavior="restore"
					enableDynamicSizing={true}
				>
					<BottomSheetView
						onLayout={dynamicContentLayoutHandle}
						style={[
							styles.bottomSheet,
							bottom > 0 && { paddingBottom: bottom },
							Platform.OS === 'android' && {
								paddingBottom: VERTICAL_DIMENS._20
							}
						]}
					>
						{step === '1' && (
							<LocationInformation
								addressDetails={addressDetails}
								onSubmit={onNext}
								loading={loading}
							/>
						)}
						{step === '2' && (
							<ShippingNumber
								loading={savingLocation}
								onSubmit={onSubmitNumber}
								shippingNumber={shippingNumber}
							/>
						)}
					</BottomSheetView>
				</BottomSheet>
			</View>
		</Modal>
	);
};

const styles = StyleSheet.create({
	modalContainer: {
		justifyContent: 'flex-end',
		margin: 0
	},
	modalContent: {
		backgroundColor: colors.grey100,
		flex: 1
	},
	modalHeader: {
		alignItems: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		flexDirection: 'row',
		width: '100%',
		height: headerHeightMobile,
		position: 'absolute'
	},
	searchInputContainer: {
		flex: 1,
		marginLeft: HORIZONTAL_DIMENS._14
	},
	searchInput: {
		backgroundColor: colors.white,
		borderRadius: 20,
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._14,
		height: 40,
		justifyContent: 'center',
		paddingLeft: HORIZONTAL_DIMENS._48,
		textAlign: I18nManager.isRTL ? 'right' : 'left',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	searchIconContainer: {
		position: 'absolute',
		height: 40,
		justifyContent: 'center',
		left: HORIZONTAL_DIMENS._16
	},
	clearSearchBtn: {
		position: 'absolute',
		height: 40,
		justifyContent: 'center',
		paddingRight: HORIZONTAL_DIMENS._10,
		right: 0
	},
	map: {
		flex: 1
	},
	markerFixed: {
		position: 'absolute',
		top: 0,
		bottom: 22,
		left: 0,
		right: 0,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'transparent'
	},
	bottomSheet: {
		paddingHorizontal: HORIZONTAL_DIMENS._16
	},
	bottomSheetIndicator: {
		backgroundColor: colors.grey300
	},
	locationBtn: {
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: 30,
		justifyContent: 'center',
		position: 'absolute',
		//bottom: '42%',
		right: 16,
		height: 54,
		width: 54,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 0
		},
		shadowOpacity: 0.14,
		shadowRadius: 2.62,
		elevation: 3
	}
});

export { SelectLocation };
