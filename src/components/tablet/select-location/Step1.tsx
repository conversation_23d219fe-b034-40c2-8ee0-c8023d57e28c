import React, { forwardRef, useEffect, useImperative<PERSON>andle, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, LayoutChangeEvent, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Col4, Row } from 'react-native-col';
import MapView, { PROVIDER_GOOGLE, Region } from 'react-native-maps';
import { useDispatch } from 'react-redux';
import {
	ArrowsRight,
	City,
	CloseCircle,
	Location,
	LocationGPS,
	LocationPin,
	Offline,
	Region as RegionIcon
} from '../../../assets/svgs/icons';
import { addCity, getTenantCities } from '../../../redux/apis/common';
import { Geocoder } from '../../../utils/functions';
import { getCurrentLocation } from '../../../utils/location';
import { colors, fonts } from '../../../utils/theme';
import { PrimaryButton } from '../../common';
import { HORIZONTAL_DIMENS, SEARCH_TYPES } from '../../../constants';
import { useAppSelector } from '../../../redux/hooks';
import { useIsConnected } from 'react-native-offline';

type Props = {
	onCancel: () => void,
	onSelect: (data?: { [key: string]: string | number; }) => void;
}

const Step1 = forwardRef(({ onCancel, onSelect }: Props, ref) => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const isConnected = useIsConnected();
	const mapRef = useRef<MapView | null>(null);
	const currentRole = useAppSelector((state) => state.auth.currentRole);
	const countryId = currentRole?.tenant_id?.country?._id;
	const countryName = currentRole?.tenant_id?.country?.name;

	const [region, setRegion] = useState<Region>({
		latitude: 37.78825,
		longitude: -122.4324,
		latitudeDelta: 0.015,
		longitudeDelta: 0.0121
	});
	const [loading, setLoading] = useState(false);
	const [bottomHeight, setBottomHeight] = useState(0);
	const [addressDetails, setAddressDetails] = useState({
		city: '',
		shippingCityId: '',
		region: '',
		shippingRegionId: '',
		country: '',
		shippingAddress: '',
		regionCode: ''
	});

	useEffect(() => {
		Geocoder.from(region?.latitude, region?.longitude)
			.then((json: any) => {
				const result = json.results[0];
				const address = Geocoder.getAddressComponents(result);
				// console.log('address', address);
				setAddressDetails(address);
			})
			.catch((error: any) => console.warn(error));
	}, [region]);

	useImperativeHandle(ref, () => ({
		getUserLocation: () => {
			getUserLocation();
		}
	}));

	/* Get user location and set region for initial map load */
	const getUserLocation = async () => {
		const location: any = await getCurrentLocation();
		if (location !== null) {
			const { coords } = location;
			setRegion({
				...region,
				latitude: coords?.latitude,
				longitude: coords?.longitude
			});
			mapRef.current?.animateToRegion({
				latitude: coords?.latitude,
				longitude: coords?.longitude,
				latitudeDelta: 0.015,
				longitudeDelta: 0.0121
			});
		}
	};

	const getCities = () => {
		return new Promise<Array<any>>(async (resolve) => {
			const selectedCity = addressDetails.city.toLowerCase();
			try {
				const params = {
					type: SEARCH_TYPES.SEARCH,
					searchKey: selectedCity
				};
				const response = await dispatch(getTenantCities(params));
				if (!response.error) {
					resolve(response.payload.data);
					return;
				}
				resolve([]);
			} catch {
				resolve([]);
			}
		});
	};

	const onNext = async () => {
		const selectedCity = addressDetails.city.toLowerCase();
		setLoading(true);
		const cities = await getCities();
		const matchingCity = cities.find(x => {
			const englishMactch = selectedCity === x.name.toLowerCase();
			const secondaryMatch = selectedCity === x.secondary_language_name?.toLowerCase();
			return englishMactch || secondaryMatch;
		});
		if (matchingCity) {
			// Allow country from the tenant country only and cities collection is subscribed for tenant country only
			if (countryId && matchingCity?.country_id?._id === countryId) {
				setLoading(false);
				if (!matchingCity.region_id?._id) {
					Alert.alert('Region not found for selected location');
				}
				onSelect({
					...addressDetails,
					shippingCityId: matchingCity._id,
					shippingRegionId: matchingCity.region_id?._id,
					...region
				});
			} else {
				setLoading(false);
				Alert.alert(`${t('select_location_within')} ${countryName}.`);
			}

		} else {
			const requestBody: any = {
				cityName: addressDetails.city,
				countryName: addressDetails.country,
				regionName: addressDetails.region
			};
			if (addressDetails.regionCode) {
				requestBody.regionCode = addressDetails.regionCode;
			}

			const response = await dispatch(addCity(requestBody));
			setLoading(false);
			if (response.error) {
				Alert.alert(t(response.payload.message));
			} else {
				dispatch(getTenantCities()); // Update cities list
				onSelect({
					...addressDetails,
					shippingCityId: response?.payload?.data?._id,
					shippingRegionId: response?.payload?.data?.region_id,
					...region
				});
			}
		}
	};

	const onUserLocation = async () => {
		const location: any = await getCurrentLocation();
		if (location !== null) {
			const { coords } = location;
			mapRef.current?.animateToRegion({
				latitude: coords?.latitude,
				longitude: coords?.longitude,
				latitudeDelta: 0.015,
				longitudeDelta: 0.0121
			});
		}
	};

	const onRegionChange = (newRegion: Region) => {
		setRegion(newRegion);
		setLoading(false);
	};

	const onBottomLayout = (e: LayoutChangeEvent) => {
		setBottomHeight(e.nativeEvent.layout.height);
	};

	return (
		<>
			<MapView
				provider={PROVIDER_GOOGLE}
				ref={mapRef}
				style={styles.map}
				initialRegion={region}
				rotateEnabled={false}
				pitchEnabled={false}
				showsUserLocation={true}
				showsMyLocationButton={false}
				showsIndoors={false}
				onRegionChangeComplete={onRegionChange}
				onRegionChangeStart={() => {
					setLoading(true);
				}}
			/>
			<View style={styles.markerFixed} pointerEvents="none">
				<LocationPin fill={colors.primary} />
			</View>
			<TouchableOpacity
				style={styles.closeButton}
				onPress={onCancel}
				disabled={loading}
			>
				<CloseCircle />
			</TouchableOpacity>
			<TouchableOpacity
				style={[styles.locationBtn, { bottom: bottomHeight + 16 }]}
				onPress={onUserLocation}
				disabled={loading}
			>
				<LocationGPS fill={colors.primary} />
			</TouchableOpacity>
			<View style={styles.footer} onLayout={onBottomLayout}>
				<Text style={styles.setLocationText}>{t('set_location')}</Text>
				<Row style={styles.addressRow}>
					<Location fill={colors.primary} />
					<Text style={styles.addressText} numberOfLines={1}>{addressDetails.shippingAddress}</Text>
				</Row>
				<Row style={styles.addressRow}>
					<RegionIcon />
					<Text style={styles.addressText} numberOfLines={1}>{addressDetails.region}</Text>
				</Row>
				<Row.C>
					<Row>
						<City fill={colors.primary} />
						<Text style={styles.addressText} numberOfLines={1}>{addressDetails.city}</Text>
					</Row>
					<Col4>
						<PrimaryButton
							title={t('next')}
							disabled={!isConnected}
							onPress={onNext}
							style={styles.submitButton}
							titleStyle={styles.submitTitle}
							rightIcon={isConnected ? <ArrowsRight stroke={colors.white} /> : <Offline stroke={colors.grey400} />}
							loading={loading}
						/>
					</Col4>
				</Row.C>
			</View>
		</>
	);
});

const styles = StyleSheet.create({
	closeButton: {
		position: 'absolute',
		top: 16,
		right: 16
	},
	map: {
		height: '100%',
		width: '100%'
	},
	markerFixed: {
		position: 'absolute',
		top: 0,
		bottom: Platform.OS === 'ios' ? 26 : 34,
		left: 0,
		right: 0,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'transparent'
	},
	locationBtn: {
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: 30,
		justifyContent: 'center',
		position: 'absolute',
		bottom: '40%',
		right: 16,
		height: 54,
		width: 54,
		shadowColor: colors.black,
		shadowOffset: {
			width: 2,
			height: 2
		},
		shadowOpacity: 0.23,
		shadowRadius: 2.62,
		elevation: 4
	},
	footer: {
		backgroundColor: colors.white,
		borderRadius: 16,
		padding: 24,
		position: 'absolute',
		left: 0,
		bottom: 0,
		right: 0
	},
	setLocationText: {
		textAlign: 'center',
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		marginBottom: 8
	},
	addressRow: {
		paddingVertical: 10
	},
	addressText: {
		color: colors.grey600,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._16,
		marginLeft: 16
	},
	submitButton: {
		alignSelf: 'flex-end',
		backgroundColor: colors.secondary,
		height: 44,
		width: 221
	},
	submitTitle: {
		fontFamily: fonts.Montserrat.Bold,
		fontWeight: '700',
		fontSize: HORIZONTAL_DIMENS._14,
		textTransform: 'uppercase'
	}
});

export default Step1;