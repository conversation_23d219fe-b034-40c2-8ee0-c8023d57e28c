import {
	Alert,
	Linking,
	Permission,
	PermissionsAndroid,
	Platform
} from 'react-native';
import axios from 'axios';
import Geolocation from 'react-native-geolocation-service';
import { setUserCurrentLocation } from '../redux/features/tracking-slice';
import { store } from '../redux/store';
import i18n from '../locales/i18n';
import NotificationService from '../services/NotificationService';

let watchId: number | undefined;
const hasPermissionIOS = async () => {
	const openSetting = () => {
		Linking.openSettings().catch(() => {
			Alert.alert(i18n.t('unable_open_setting'));
		});
	};
	const status = await Geolocation.requestAuthorization('always');

	if (status === 'granted') {
		return true;
	}

	if (status === 'denied') {
		Alert.alert(i18n.t('location_permission_denied'));
	}

	if (status === 'disabled') {
		Alert.alert(
			i18n.t('turn_on_location'),
			'',
			[
				{ text: 'Go to Settings', onPress: openSetting },
				{ text: "Don't Use Location", onPress: () => { } }
			]
		);
	}

	return false;
};

const hasLocationPermission = async () => {
	if (Platform.OS === 'ios') {
		const hasPermission = await hasPermissionIOS();
		return hasPermission;
	}

	if (Platform.OS === 'android' && Platform.Version < 23) {
		return true;
	}

	let alwaysPermission: Permission;
	if (Number(Platform.Version) < 29) {
		alwaysPermission = PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION;
	} else {
		alwaysPermission = PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION;
		// alwaysPermission = PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION;
	}

	const hasPermission = await PermissionsAndroid.check(alwaysPermission);

	if (hasPermission) {
		return true;
	}

	const status = await PermissionsAndroid.request(alwaysPermission);

	if (status === PermissionsAndroid.RESULTS.GRANTED) {
		return true;
	}

	if (status === PermissionsAndroid.RESULTS.DENIED) {
		Alert.alert(i18n.t('location_denied_user'));
	} else if (status === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
		Alert.alert(i18n.t('location_revoke_user'));
	}

	return false;
};

export const getCurrentLocation = (options?: {
	timeout?: number;
	maximumAge?: number;
	enableHighAccuracy?: boolean;
	showErrorAlert?: boolean;
}) => {
	const {
		timeout = 20000, // Increased timeout for better accuracy
		maximumAge = 5000, // Reduced cache time for fresher location
		enableHighAccuracy = true,
		showErrorAlert = true
	} = options || {};

	return new Promise(async (resolve) => {
		const hasPermission = await hasLocationPermission();

		if (!hasPermission) {
			resolve(null);
			return;
		}

		Geolocation.getCurrentPosition(
			(position) => {
				// Validate location accuracy
				const accuracy = position.coords.accuracy;
				console.log('Location accuracy:', accuracy, 'meters');
				resolve(position);
			},
			(error) => {
				console.log('getCurrentPosition error:', error);

				if (showErrorAlert) {
					let errorMessage = '';
					switch (error.code) {
						case 1: // PERMISSION_DENIED
							errorMessage = i18n.t('location_permission_denied');
							break;
						case 2: // POSITION_UNAVAILABLE
							errorMessage = i18n.t('failed_to_load_location');
							break;
						case 3: // TIMEOUT
							errorMessage = i18n.t('location_timeout');
							break;
						default:
							errorMessage = error.message || i18n.t('location_error');
					}
					Alert.alert(i18n.t('location_error'), errorMessage);
				}

				resolve(null);
			},
			{
				accuracy: {
					android: 'high',
					ios: 'best'
				},
				timeout,
				maximumAge,
				distanceFilter: 0,
				forceRequestLocation: true,
				enableHighAccuracy
			}
		);
	});
};

type WatchLocationArgs = {
	canStartForegroundService?: boolean;
	distanceFilter?: number;
}

export const watchLocation = async ({
	canStartForegroundService,
	distanceFilter = 20
}: WatchLocationArgs) => {
	console.log('watchLocation called', watchId);
	const hasPermission = await hasLocationPermission();

	if (!hasPermission) {
		return;
	}

	if (watchId !== undefined) {
		Geolocation.clearWatch(watchId);
		watchId = undefined;
		Geolocation.stopObserving();
		console.log('create watch after clear current watch');
	}

	watchId = Geolocation.watchPosition(
		(position) => {
			// console.log('watchPosition:', position.coords);
			store.dispatch(setUserCurrentLocation(position.coords));
		},
		(error) => {
			console.log('watchPosition error', error);
		},
		{
			accuracy: {
				android: 'high',
				ios: 'best'
			},
			// accuracy: {
			// 	android: 'high',
			// 	ios: 'best'
			// },
			// enableHighAccuracy: true,
			// interval: 15000,
			// fastestInterval: 10000,
			distanceFilter: distanceFilter,
			forceRequestLocation: true
			// useSignificantChanges: true
		}
	);

	if (canStartForegroundService) {
		try {
			// Start foreground service
			await NotificationService.startForegroundService();
		} catch (error) {
			console.error('Error starting foreground service:', error);
		}
	}
	console.log('watchId', watchId);
};

export const stopWatchLocation = async () => {
	console.log('stopWatchLocation called', watchId);

	// Clear the watch if it exists
	if (watchId !== undefined) {
		try {
			Geolocation.clearWatch(watchId);
		} catch (error) {
			console.error('Error clearing watch:', error);
		}
	}

	// Stop the foreground service
	try {
		await NotificationService.stopForegroundService();
	} catch (error) {
		console.error('Error stopping foreground service:', error);
	}

	// Reset watch ID and stop observing
	watchId = undefined;

	try {
		Geolocation.stopObserving();
	} catch (error) {
		console.error('Error stopping location observing:', error);
	}
};


// trying to sanitize floating point fuckups here to a certain extent
const imprecise = (number: number, decimals: number = 4) => {
	const factor = Math.pow(10, decimals);
	return Math.round(number * factor) / factor;
};

// Converts a decimal coordinate value to sexagesimal format
export const decimalToSexagesimal = (decimal: number) => {
	const [pre, post] = decimal?.toString().split('.');

	const deg = Math.abs(Number(pre));
	const min0 = Number('0.' + (post || 0)) * 60;
	const sec0 = min0?.toString().split('.');

	const min = Math.floor(min0);
	const sec = imprecise(Number('0.' + (sec0[1] || 0)) * 60)?.toString();

	const [secPreDec, secDec = '0'] = sec.split('.');

	return (
		deg +
		'° ' +
		min?.toString().padStart(2, '0') +
		"' " +
		secPreDec.padStart(2, '0') +
		'.' +
		secDec.padEnd(1, '0') +
		'"'
	);
};

export const snapToRoad = (userLocation: any, apiKey: string) => {
	return new Promise((resolve) => {
		axios
			.get(
				`https://roads.googleapis.com/v1/snapToRoads?path=${userLocation?.latitude},${userLocation?.longitude}&key=${apiKey}`,
				{
					headers: {
						'Cache-Control': 'no-cache'
					}
				}
			)
			.then((response) => {
				const snappedPoints = response.data?.snappedPoints;

				// Use the snappedPoints to update the marker's position
				if (snappedPoints && snappedPoints?.length > 0) {
					const firstSnappedPoint = snappedPoints[0];
					const markerPosition = {
						latitude: firstSnappedPoint.location?.latitude,
						longitude: firstSnappedPoint.location?.longitude
					};
					// Update the marker's position with markerPosition
					resolve(markerPosition);
				} else {
					resolve(userLocation);
				}
			})
			.catch((error) => {
				resolve(userLocation);
				console.error('Error fetching snapped points:', error);
			});
	});
};