import { useFocusEffect, useNavigation } from '@react-navigation/native';
import React, { memo, useCallback, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { I18nManager, Text, View } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { RecommendedCardTabletOffline } from '../../components/product-cards';
import { useAppSelector } from '../../redux/hooks';
import { getPriceListObject } from '../../utils/functions';
import { getPriceListId } from '../../redux/selectors';
import { setProductListPrice, setSelectedProduct } from '../../redux/features/productDetails-slice';
import styles from './styles';

const RestockedProductsOffline = memo(() => {
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const navigation = useNavigation<any>();
	const priceListId = useAppSelector(getPriceListId);
	const allProducts = useAppSelector(state => state.category.allProducts);
	const [RestockedProducts, setRestockedProducts] = useState<any>([]);
	// const newProducts = useAppSelector(getNewProductsForTablet);

	useFocusEffect(
		useCallback(() => {
			setTimeout(() => {

				const products = allProducts.length > 0 ? allProducts.filter((product: any) => {
					return product.is_restocked;
				}).sort((a, b) => new Date(b.restocked_at).getTime() - new Date(a.restocked_at).getTime()) : [];

				setRestockedProducts(products);
			}, 0);
		}, [allProducts.length])
	);

	const openProductDetails = useCallback((product: any) => {
		const parsedProduct = JSON.parse(JSON.stringify(product));
		const finalPriceListItem = getPriceListObject(parsedProduct.price_mappings, priceListId);
		dispatch(setProductListPrice(finalPriceListItem));
		dispatch(setSelectedProduct(parsedProduct._id));
		navigation.navigate('CatalogVariant', { productId: parsedProduct._id });
	}, [priceListId]);

	const renderProductItem = useCallback(({ item }: any) => {
		return (
			<RecommendedCardTabletOffline
				item={item}
				containerStyle={styles.productCardContainer}
				onPress={openProductDetails}
			/>
		);
	}, []);

	const renderItemSeparator = useCallback(() => {
		return <View style={styles.productCardSeparator} />;
	}, []);

	const keyExtractor = useCallback((item: any, index: number) => `product-${item._id}-${index}`, []);

	return (
		<View style={styles.container}>
			<FlashList
				contentContainerStyle={styles.listContainer}
				showsVerticalScrollIndicator={false}
				data={RestockedProducts}
				renderItem={renderProductItem}
				ItemSeparatorComponent={renderItemSeparator}
				numColumns={6}
				estimatedItemSize={300}
				keyExtractor={keyExtractor}
				ListEmptyComponent={<Text style={styles.noResults}>{t('no_result_found')}</Text>}
				keyboardShouldPersistTaps='handled'
				disableAutoLayout={I18nManager.isRTL}
			/>
		</View>
	);
});

export default RestockedProductsOffline;