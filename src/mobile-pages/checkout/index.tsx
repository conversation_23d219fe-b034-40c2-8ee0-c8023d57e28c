import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Alert, Platform, SafeAreaView, Text, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { CommonActions, useFocusEffect, useNavigation } from '@react-navigation/native';
import { Row } from 'react-native-col';
import { Gift2 } from '../../assets/svgs/icons';
import { PriceDecimal, PrimaryButton } from '../../components/common';
import { CustomerAddressInfo, OrderSuccessModal, SelectLocation, ShippingNumberBottomSheet } from '../../components/mobile';
import { colors } from '../../utils/theme';
import { useAppSelector } from '../../redux/hooks';
import { checkBadRequest, priceFormat } from '../../utils/functions';
import {
	getBranchId,
	getCatalogModeSetting,
	getCustomerExternalId,
	getCustomerLegalName,
	getCustomerName,
	getCustomerUserRoleId,
	getPrimaryContactName,
	getSalesPersonName,
	getSalesPersonRoleId,
	getUserType
} from '../../redux/selectors';
import { placeOrder } from '../../redux/apis/cart';
import { clearCartList, setNewShippingDetails, setShippingNumberDetails } from '../../redux/features/cart-slice';
import { DEALS_TYPES } from '../../constants';
import styles from './styles';
import Toast from 'react-native-toast-message';
import { validateObject } from '../../utils/validateObject';
import { setSentryLog } from '../../utils/sentry';

const Checkout = () => {
	const { t, i18n } = useTranslation();
	const dispatch = useDispatch();
	const navigation = useNavigation<any>();
	const cartProducts = useAppSelector(state => state.cart.cartProducts);
	const taxData = useAppSelector(state => state.cart.taxData);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const cartProductsInfo = useAppSelector(state => state.cart.cartProductsInfo);
	const shippingDetails = useAppSelector(state => state.cart.shippingDetails);
	const taxSettings = useAppSelector(state => state.common.taxSettings);
	const userType = useAppSelector(getUserType);
	const catalogMode = useAppSelector(getCatalogModeSetting);
	const customerName = useAppSelector(getCustomerName);
	const customerLegalName = useAppSelector(getCustomerLegalName);
	const salePersonName = useAppSelector(getSalesPersonName);
	const primaryContact = useAppSelector(getPrimaryContactName);
	const customerExternalId = useAppSelector(getCustomerExternalId);
	const customerUserRoleId = useAppSelector(getCustomerUserRoleId);
	const salesPersonRoleId = useAppSelector(getSalesPersonRoleId);
	const branchId = useAppSelector(getBranchId);
	const shippingNumberSheetRef = useRef<any>(null);

	const [orderId, setOrderId] = useState<string>('');
	const [showLocationModal, setShowLocationModal] = useState(false);
	const [loading, setLoading] = useState(false);
	const vatInPercent = priceFormat((cartProductsInfo.vatTotal / cartProductsInfo.totalBeforeTax) * 100);

	// Remove checkout from navigation history when user leaves this page
	useFocusEffect(
		useCallback(() => {
			// This runs when the screen gains focus
			return () => {
				// This cleanup runs when the screen loses focus (user navigates away)
				// Remove checkout from navigation stack to prevent going back to empty checkout
				setTimeout(() => {
					const state = navigation.getState();
					const routes = state.routes;
					const checkoutIndex = routes.findIndex((route: any) => route.name === 'Checkout');

					if (checkoutIndex !== -1 && checkoutIndex < routes.length - 1) {
						// Checkout is not the current screen, so we can safely remove it
						const newRoutes = routes.filter((_: any, index: number) => index !== checkoutIndex);
						const resetAction = CommonActions.reset({
							index: state.index - 1, // Adjust index since we removed one route
							routes: newRoutes
						});
						navigation.dispatch(resetAction);
					}
				}, 100);
			};
		}, [navigation])
	);

	const toggleLocationModal = () => {
		setShowLocationModal(!showLocationModal);
	};

	const CallPlaceOrder = async () => {
		if (loading) {
			const shippingMobileNumber = shippingDetails.shippingMobileNumber;
			if (!+shippingMobileNumber) {
				shippingNumberSheetRef.current?.showShippingNumberBottomSheet();
				setLoading(false);
				return;
			}
			try {
				const requestBody: any = {
					tenantId: currentRole?.tenant_id?._id,
					salesPersonRoleId: salesPersonRoleId,
					customerUserRoleId: customerUserRoleId,
					customerName: customerName,
					salePersonName: salePersonName,
					branchId: branchId,
					orderPunchDeviceType: 'MOBILE',
					orderPunchDeviceOs: Platform.OS.toUpperCase(),
					orderAppType: userType,
					ShippingAddress: shippingDetails.shippingAddress,
					cityId: shippingDetails.shippingCityId,
					regionId: shippingDetails.shippingRegionId,
					shippingMobileNumber: shippingDetails.shippingMobileNumber,
					shippingCountryCode: shippingDetails.shippingCountryCode,
					regionName: shippingDetails.region,
					cityName: shippingDetails.city,
					shippingCoordinates: {
						lat: shippingDetails?.latitude,
						lng: shippingDetails?.longitude
					},
					customerPrimaryContactName: primaryContact,
					externalId: customerExternalId,
					customer_legal_name: customerLegalName,
					apiVersion: 2
				};
				const NullishKeys = validateObject(requestBody,
					[
						'tenantId',
						'customerUserRoleId',
						'customerName',
						'customerPrimaryContactName',
						'salesPersonRoleId',
						'salePersonName',
						'branchId',
						'customer_legal_name',
						'orderPunchDeviceType',
						'orderPunchDeviceOs',
						'orderAppType',
						'ShippingAddress',
						'cityId',
						'regionId',
						'shippingMobileNumber',
						'shippingCountryCode',
						'regionName',
						'cityName',
						'shippingCoordinates.lat',
						'shippingCoordinates.lng']);
				// console.log("🚀 ~ PlaceOrder ~ NullishKeys:", NullishKeys)
				if (NullishKeys && Array.isArray(NullishKeys) && NullishKeys.length === 0) {
					const response = await dispatch(placeOrder(requestBody));
					// console.log('response of place order', JSON.stringify(response.payload));
					if (!response.error) {
						setOrderId(response.payload.data._id);
						dispatch(clearCartList());
					} else {
						//Alert.alert(t(response.payload.message));
						Alert.alert(t(checkBadRequest(response.payload)));
						setSentryLog('placeOrderAPIError', '/productService/order', 'post', `${requestBody}`, JSON.stringify(response));
					}

				} else {
					setSentryLog('placeOrderAPIError', '/productService/order', 'post', `${requestBody}`, `Invalid fields: ${NullishKeys.join(', ')}`);
					Alert.alert(`Missing fields values: ${NullishKeys.join(', ')}`);
				}

			} catch (error) {
				console.log('In catch', error);
				Alert.alert('Something went wrong!');
				setSentryLog('placeOrderAPIError', '/productService/order', 'post', 'Catch Error', JSON.stringify(error));

			} finally {
				setLoading(false);
			}
		}
	};

	useEffect(() => {
		CallPlaceOrder();
	}, [loading]);

	const handlePlaceOrder = async () => {
		if (!loading) {
			setLoading(true);
		}
	};

	const onSelectLocation = (locationDetails: any) => {
		dispatch(setNewShippingDetails(locationDetails));
		//setShippingDetails(locationDetails);
		toggleLocationModal();
	};

	const onClose = (type: string, fromNavigation?: boolean) => {
		setOrderId(''); //Close

		// If modal is being closed due to navigation (e.g., notification click),
		// do absolutely nothing - let the notification navigation handle everything
		// The useFocusEffect will handle removing checkout from navigation history
		if (fromNavigation) {
			return; // Just close the modal, don't interfere with navigation at all
		}

		// For manual closes, handle navigation appropriately
		if (type === 'home') {
			// Reset navigation stack to AppStack (which contains the bottom tabs including Home)
			const resetAction = CommonActions.reset({
				index: 0,
				routes: [{ name: 'AppStack' }]
			});
			navigation.dispatch(resetAction);
		} else {
			// For regular close (X button, backdrop), just go back
			// This will go back to Cart (which will show empty cart message)
			navigation.goBack();
		}
	};

	const onSetShippingNumber = (shippingCountryCode: string, shippingMobileNumber: string) => {
		dispatch(setShippingNumberDetails({
			shippingCountryCode,
			shippingMobileNumber
		}));

	};

	const getOrderItemPrice = useCallback((orderItem: any) => {
		if (orderItem && orderItem.deal_info) {
			if (orderItem.deal_info.deal_type === DEALS_TYPES.DISCOUNT) {
				return orderItem.deal_info.discounted_price;
			} else if (orderItem.deal_info.deal_type === DEALS_TYPES.BULK_PRICING) {
				return orderItem.deal_info.bulk_price;
			}
		}
		return orderItem.original_price;
	}, []);
	useEffect(() => {
		// console.log('🚀 ~ useEffect ~ shippingDetails:', shippingDetails);
		if (shippingDetails === null) {
			Toast.show({
				text1: t('ask_the_salesperson_to_update_the_address'),
				type: 'normal',
				position: 'top'
			});
		}

	}, [shippingDetails]);

	const listProductDetails = (item: any, index: number) => (
		<View key={item._id + index}>
			<View style={styles.productContainer}>
				<View style={styles.nameContainer}>
					<Text style={styles.productName}>
						{i18n.language === 'en' ? item.product_name : item.product_secondary_name}
						<Text>{item.variant_name ? '- ' : ''}</Text>
						<Text>{item.group_name ? item.group_name + ' / ' + item.variant_name : item.variant_name}</Text>
					</Text>
				</View>
				<View style={styles.quantityContainer}>
					<Text style={styles.productQuantity} numberOfLines={2}>{item.quantity}</Text>
				</View>
				<View style={styles.priceContainer}>
					<PriceDecimal
						value={getOrderItemPrice(item)}
						style={[styles.productDealPrice, catalogMode && styles.hidePrice]}
						currencyStyle={styles.currencyText}
					/>
					{item.deal_info && (item.deal_info.deal_type === DEALS_TYPES.DISCOUNT || (item.deal_info.deal_type === DEALS_TYPES.BULK_PRICING && getOrderItemPrice(item) < item.original_price)) && <PriceDecimal
						value={item.original_price}
						style={styles.originalPriceText}
					/>}
				</View>
			</View>

			{item.deal_info && item.deal_info.deal_type === DEALS_TYPES.BUY_X_AND_GET_Y && item.deal_info?.free_item_count > 0 && <View style={styles.productContainer}>
				<View style={styles.flexRow}>
					<Gift2 style={styles.giftIcon} />
					<View style={styles.nameContainer}>
						<Text style={styles.productName} numberOfLines={2}>
							{i18n.language === 'en' ? item.product_name : item.product_secondary_name}
							<Text>{item.variant_name ? '- ' : ''}</Text>
							<Text>{item.group_name ? item.group_name + ' / ' + item.variant_name : item.variant_name}</Text>
						</Text>
					</View>
				</View>

				<View style={styles.quantityContainer}><Text style={styles.productQuantity} numberOfLines={2}>{item.deal_info.free_item_count}</Text></View>
				<View style={styles.priceContainer}>
					<PriceDecimal
						value={0}
						style={styles.productDealPrice}
					/>
				</View>
			</View>}
		</View>
	);

	//console.log('details', details);

	return (
		<SafeAreaView style={styles.safeAreaView}>
			<KeyboardAwareScrollView
				style={styles.scrollContainer}
				showsVerticalScrollIndicator={false}
				extraHeight={-64}
			>
				<View style={styles.container}>
					<View style={styles.main}>

						<View style={styles.addressContent}>
							{/* {
								shippingDetails === null &&
								<View style={styles.locationPicker}>
									<Text style={styles.addressHeading}>{t('delivery_address')}:</Text>
									<TouchableOpacity style={styles.addLocationBtn} onPress={toggleLocationModal}>
										<View style={styles.plusCircle}>
											<PlusVector fill={colors.white} />
										</View>
										<Text style={styles.addLocationTxt}>{t('add_location')}</Text>
									</TouchableOpacity>
								</View>
							} */}
							{
								shippingDetails !== null &&
								<View style={styles.customlocationContainer}>
									<CustomerAddressInfo
										header={t('delivery_address')}
										pencilTintColor={colors.primary}
										upperContainer={true}
										addressInfo={shippingDetails}
										onEdit={toggleLocationModal}
									/>
								</View>
							}
							<SelectLocation
								isVisible={showLocationModal}
								onClose={toggleLocationModal}
								onSelect={onSelectLocation}
								customerRoleId={customerUserRoleId}
								initialLocation={shippingDetails ? {
									latitude: shippingDetails.latitude,
									longitude: shippingDetails.longitude,
									address: shippingDetails.shippingAddress,
									city: shippingDetails.city,
									region: shippingDetails.region,
									country: shippingDetails.country
								} : undefined}
							/>
						</View>

						<View style={styles.orderDetailsContainer}>
							<Text style={styles.orderDetails}>{t('order_details')}:</Text>
						</View>
						{cartProducts.map(listProductDetails)}

						<View style={[styles.orderSummeryContainer, catalogMode && styles.hidePrice]}>
							<Text style={styles.orderSummery}>{t('order_summary')}</Text>
							<View style={styles.container_row}>
								<Text style={styles.container_text}>{t('total_before_vat')}</Text>
								<PriceDecimal
									value={cartProductsInfo.totalBeforeTax}
									style={styles.container_text}
								/>
							</View>
							{/* <View style={styles.container_row}>
								<Text style={styles.container_text}>{t('vat')}{` (${vatInPercent}%)`}</Text>
								<PriceDecimal
									value={cartProductsInfo.vatTotal}
									style={styles.container_text}
								/>
							</View> */}
							{
								taxData.map((taxItem) => taxItem.type === 'GROUP' ? (
									<View key={taxItem.tax_id}>
										<View style={styles.container_row}>
											<Text style={styles.container_text}>{taxItem.tax_name}</Text>
											<PriceDecimal
												value={taxItem?.calculated_tax}
												style={styles.container_text}
											/>
										</View>
										{
											taxItem.group_taxes.map((groupItem: any) => (
												<View style={[styles.container_row, styles.groupTextRow]} key={groupItem.tax_id}>
													<Text style={styles.container_text}>{groupItem.tax_name}</Text>
													<Row.C>
														<PriceDecimal
															value={groupItem?.calculated_tax}
															style={styles.container_text}
														/>
													</Row.C>
												</View>
											))
										}
									</View>
								) : (
									<Row.LR style={styles.container_row} key={taxItem.tax_id}>
										<Text style={styles.container_text}>{taxItem.tax_name}</Text>
										<PriceDecimal value={taxItem?.calculated_tax} style={styles.container_text} />
									</Row.LR>
								))
							}
							<View style={[styles.container_row, styles.totalContainer]}>
								<Text style={styles.container_text}>{t('total_amount')} {t('vat_inclusive')}</Text>
								<PriceDecimal
									value={cartProductsInfo.totalCheckout}
									style={styles.container_text}
								/>
							</View>
						</View>
					</View>
				</View>
			</KeyboardAwareScrollView >

			<View style={styles.footerContainer}>
				<View style={[styles.container_row, styles.totalFooterContainer]}>
					<Text style={styles.orderSummery}>{t('total_amount')}</Text>
					<PriceDecimal
						value={cartProductsInfo.totalCheckout}
						style={[styles.orderSummery, catalogMode && styles.hidePrice]}
					/>
				</View>
				<Text
					style={[
						styles.footerText,
						catalogMode && styles.hidePrice,
						taxSettings?.price === 'EXCLUDE' && styles.hidePrice
					]}
				>
					{t('prices_vat_inclusive', { count: Number(vatInPercent) })}
				</Text>
				<View style={styles.footerBtn}>
					<PrimaryButton
						title={t(catalogMode ? 'request_price' : 'place_order')}
						onPress={handlePlaceOrder}
						disabled={shippingDetails ? false : true || loading}
						titleStyle={styles.activeBtnText}
						style={styles.activeBtn}
						loading={loading}
					/>
				</View>
			</View>

			<OrderSuccessModal
				isVisible={orderId !== ''}
				// isVisible={true}
				onCancel={onClose}
				orderId={orderId}
			/>

			<ShippingNumberBottomSheet
				ref={shippingNumberSheetRef}
				onSelect={onSetShippingNumber}
			/>

		</SafeAreaView >

	);
};

export default Checkout;
