// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* hawakTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* hawakTests.m */; };
		0C6F484E6884447DB2CE30D5 /* Montserrat-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 073316EDB5354E349C82D29B /* Montserrat-Bold.ttf */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		17B40FE3B08D43B5BCBC4F09 /* Cairo-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0F878B5DEA0142A0B96F4AB7 /* Cairo-Bold.ttf */; };
		1C880F1A960344A8A3075C2E /* Cairo-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AD358BC881214B18B8BA53A8 /* Cairo-Regular.ttf */; };
		37E9E6002F974C27A32FF814 /* Cairo-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 341DB18F60234AE9B1AD32AA /* Cairo-ExtraBold.ttf */; };
		39311FB3312549FCBD775CDF /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F575DA55B0EE49479DD35024 /* BootSplash.storyboard */; };
		3D8BAC6BC7454529B2EBA875 /* Almarai-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB083238899647EFAB2C06F4 /* Almarai-Bold.ttf */; };
		41C34EFCDEA44009A778C111 /* Montserrat-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AE794EE0731B4816A4ACCC1E /* Montserrat-Light.ttf */; };
		47CA28E1A6414FF892C7A91F /* Montserrat-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 83F8F914BF3F43F0854D6939 /* Montserrat-SemiBold.ttf */; };
		5380B24B50B14C10800FF8DC /* Cairo-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E88E6C1896AC4F6E8D4CB04B /* Cairo-Light.ttf */; };
		55B0B0675C9EDDFF3AC355F3 /* Pods_hawakCommonPods_hawak_staging.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A21597C28E8D755CD2F53B61 /* Pods_hawakCommonPods_hawak_staging.framework */; };
		725525DB5FEC4D94ADC0FE2D /* Montserrat-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AB049AB5240346A490DEB7D8 /* Montserrat-Medium.ttf */; };
		7260FC4312EE4779842A26D4 /* Colors.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 612D0E3D3FA34BD59C87125E /* Colors.xcassets */; };
		79F08C6523844AFCA2F85CC3 /* Montserrat-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3FAE459DAB60492C95E0B1C3 /* Montserrat-Regular.ttf */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		981009573B984768AAB58544 /* Almarai-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 1F0AC1AF61DB4B9C882284FA /* Almarai-ExtraBold.ttf */; };
		9AB857F2BE99DFF67D29A628 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 14D1D27A9A375C0FA0FC6356 /* PrivacyInfo.xcprivacy */; };
		9D1DFD36B7234AE58E2CC464 /* Almarai-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EAD92EB75F584F22B3730E84 /* Almarai-Regular.ttf */; };
		A6436E3E0A4E4407875CE6B2 /* Almarai-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E5568755FC1745EE8874395F /* Almarai-Light.ttf */; };
		AFE5A802AF30F7BDAFBDC3B1 /* Pods_hawakCommonPods_hawakTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3EF50B0E82DAA06D1FCC45B9 /* Pods_hawakCommonPods_hawakTests.framework */; };
		BEF177BE2A7F40F7AC8F27FA /* Pods_hawakCommonPods_hawak.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1B4FEA7FE89134B33432A86 /* Pods_hawakCommonPods_hawak.framework */; };
		C1CDE67576A94BCB8C6D8956 /* Cairo-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 14950E11C7B346E3A287D756 /* Cairo-Medium.ttf */; };
		C719CC1111A544718D51B634 /* Cairo-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 59C0F7035730474BB3298427 /* Cairo-SemiBold.ttf */; };
		E50D1EB82C900734003256D5 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = E50D1EB12C900734003256D5 /* GoogleService-Info.plist */; };
		E50D1EBA2C900734003256D5 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = E50D1EB32C900734003256D5 /* GoogleService-Info.plist */; };
		E50D1EBC2C900734003256D5 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = E50D1EB52C900734003256D5 /* GoogleService-Info.plist */; };
		E52285642C8EE7D1003A4D4B /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		E52285652C8EE7D1003A4D4B /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		E52285692C8EE7D1003A4D4B /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		E522856A2C8EE7D1003A4D4B /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		E522856B2C8EE7D1003A4D4B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 14D1D27A9A375C0FA0FC6356 /* PrivacyInfo.xcprivacy */; };
		E52285742C8EE831003A4D4B /* Development-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = E52285732C8EE7D1003A4D4B /* Development-Info.plist */; };
		E52285782C8EE9F9003A4D4B /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		E52285792C8EE9F9003A4D4B /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		E522857D2C8EE9F9003A4D4B /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		E522857E2C8EE9F9003A4D4B /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		E522857F2C8EE9F9003A4D4B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 14D1D27A9A375C0FA0FC6356 /* PrivacyInfo.xcprivacy */; };
		E52285882C8EEA23003A4D4B /* Staging-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = E52285872C8EE9F9003A4D4B /* Staging-Info.plist */; };
		E522858A2C8EEAF8003A4D4B /* Config.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = E52285892C8EEAF8003A4D4B /* Config.xcconfig */; };
		E58569F12CABCA8E00E36F09 /* scanner_beep.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = E58569F02CABCA8E00E36F09 /* scanner_beep.mp3 */; };
		E58569F22CABCA8E00E36F09 /* scanner_beep.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = E58569F02CABCA8E00E36F09 /* scanner_beep.mp3 */; };
		E58569F32CABCA8E00E36F09 /* scanner_beep.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = E58569F02CABCA8E00E36F09 /* scanner_beep.mp3 */; };
		E5D48FB42C92C2B0008ED676 /* payment_Success.m4a in Resources */ = {isa = PBXBuildFile; fileRef = E5D48FB32C92C2AF008ED676 /* payment_Success.m4a */; };
		E5D48FB52C92C2B0008ED676 /* payment_Success.m4a in Resources */ = {isa = PBXBuildFile; fileRef = E5D48FB32C92C2AF008ED676 /* payment_Success.m4a */; };
		E5D48FB62C92C2B0008ED676 /* payment_Success.m4a in Resources */ = {isa = PBXBuildFile; fileRef = E5D48FB32C92C2AF008ED676 /* payment_Success.m4a */; };
		E5D9DE802CCF8254003E813D /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F575DA55B0EE49479DD35024 /* BootSplash.storyboard */; };
		E5D9DE812CCF8254003E813D /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F575DA55B0EE49479DD35024 /* BootSplash.storyboard */; };
		E5E40CD12C92B0D800E680DF /* geolocation.swift in Sources */ = {isa = PBXBuildFile; fileRef = E5E40CD02C92B0D800E680DF /* geolocation.swift */; };
		E5E40CD22C92B0D800E680DF /* geolocation.swift in Sources */ = {isa = PBXBuildFile; fileRef = E5E40CD02C92B0D800E680DF /* geolocation.swift */; };
		E5E40CD32C92B0D800E680DF /* geolocation.swift in Sources */ = {isa = PBXBuildFile; fileRef = E5E40CD02C92B0D800E680DF /* geolocation.swift */; };
		FE07221F4A1AAE4A048E5D22 /* Pods_hawakCommonPods_hawak_development.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4F540CEDE1160BDB6260E2E6 /* Pods_hawakCommonPods_hawak_development.framework */; };
		FF72BC7B78A9495AA979050B /* Montserrat-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BEA8BCBCEDF24C58B92CD984 /* Montserrat-ExtraBold.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = hawak;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* hawakTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = hawakTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* hawakTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = hawakTests.m; sourceTree = "<group>"; };
		073316EDB5354E349C82D29B /* Montserrat-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Bold.ttf"; path = "../src/assets/fonts/Montserrat/Montserrat-Bold.ttf"; sourceTree = "<group>"; };
		0F878B5DEA0142A0B96F4AB7 /* Cairo-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Cairo-Bold.ttf"; path = "../src/assets/fonts/Cairo/Cairo-Bold.ttf"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* hawak.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = hawak.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = hawak/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = hawak/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = hawak/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = hawak/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = hawak/main.m; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = hawak/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		14950E11C7B346E3A287D756 /* Cairo-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Cairo-Medium.ttf"; path = "../src/assets/fonts/Cairo/Cairo-Medium.ttf"; sourceTree = "<group>"; };
		14D1D27A9A375C0FA0FC6356 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = hawak/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		1F0AC1AF61DB4B9C882284FA /* Almarai-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Almarai-ExtraBold.ttf"; path = "../src/assets/fonts/Almarai/Almarai-ExtraBold.ttf"; sourceTree = "<group>"; };
		33578BED5BCE38B352B9C511 /* Pods-hawakCommonPods-hawakTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-hawakCommonPods-hawakTests.release.xcconfig"; path = "Target Support Files/Pods-hawakCommonPods-hawakTests/Pods-hawakCommonPods-hawakTests.release.xcconfig"; sourceTree = "<group>"; };
		341DB18F60234AE9B1AD32AA /* Cairo-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Cairo-ExtraBold.ttf"; path = "../src/assets/fonts/Cairo/Cairo-ExtraBold.ttf"; sourceTree = "<group>"; };
		3EF50B0E82DAA06D1FCC45B9 /* Pods_hawakCommonPods_hawakTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_hawakCommonPods_hawakTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3FAE459DAB60492C95E0B1C3 /* Montserrat-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Regular.ttf"; path = "../src/assets/fonts/Montserrat/Montserrat-Regular.ttf"; sourceTree = "<group>"; };
		3FFB094AF6F2A1D8A2705D51 /* Pods-hawakCommonPods-hawak-development.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-hawakCommonPods-hawak-development.debug.xcconfig"; path = "Target Support Files/Pods-hawakCommonPods-hawak-development/Pods-hawakCommonPods-hawak-development.debug.xcconfig"; sourceTree = "<group>"; };
		4F540CEDE1160BDB6260E2E6 /* Pods_hawakCommonPods_hawak_development.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_hawakCommonPods_hawak_development.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		59C0F7035730474BB3298427 /* Cairo-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Cairo-SemiBold.ttf"; path = "../src/assets/fonts/Cairo/Cairo-SemiBold.ttf"; sourceTree = "<group>"; };
		612D0E3D3FA34BD59C87125E /* Colors.xcassets */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = folder.assetcatalog; name = Colors.xcassets; path = hawak/Colors.xcassets; sourceTree = "<group>"; };
		64EE896E649BFADAB098819B /* Pods-hawakCommonPods-hawakTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-hawakCommonPods-hawakTests.debug.xcconfig"; path = "Target Support Files/Pods-hawakCommonPods-hawakTests/Pods-hawakCommonPods-hawakTests.debug.xcconfig"; sourceTree = "<group>"; };
		77230F6B47EEC6CE827FD693 /* Pods-hawakCommonPods-hawak-staging.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-hawakCommonPods-hawak-staging.release.xcconfig"; path = "Target Support Files/Pods-hawakCommonPods-hawak-staging/Pods-hawakCommonPods-hawak-staging.release.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = hawak/LaunchScreen.storyboard; sourceTree = "<group>"; };
		83F8F914BF3F43F0854D6939 /* Montserrat-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-SemiBold.ttf"; path = "../src/assets/fonts/Montserrat/Montserrat-SemiBold.ttf"; sourceTree = "<group>"; };
		86D5F686AC31B8C1B86D4856 /* Pods-hawakCommonPods-hawak.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-hawakCommonPods-hawak.release.xcconfig"; path = "Target Support Files/Pods-hawakCommonPods-hawak/Pods-hawakCommonPods-hawak.release.xcconfig"; sourceTree = "<group>"; };
		A183A7C13FF1EAB0D177D40E /* Pods-hawakCommonPods-hawak-development.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-hawakCommonPods-hawak-development.release.xcconfig"; path = "Target Support Files/Pods-hawakCommonPods-hawak-development/Pods-hawakCommonPods-hawak-development.release.xcconfig"; sourceTree = "<group>"; };
		A1B4FEA7FE89134B33432A86 /* Pods_hawakCommonPods_hawak.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_hawakCommonPods_hawak.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A21597C28E8D755CD2F53B61 /* Pods_hawakCommonPods_hawak_staging.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_hawakCommonPods_hawak_staging.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AB049AB5240346A490DEB7D8 /* Montserrat-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Medium.ttf"; path = "../src/assets/fonts/Montserrat/Montserrat-Medium.ttf"; sourceTree = "<group>"; };
		AD358BC881214B18B8BA53A8 /* Cairo-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Cairo-Regular.ttf"; path = "../src/assets/fonts/Cairo/Cairo-Regular.ttf"; sourceTree = "<group>"; };
		AE794EE0731B4816A4ACCC1E /* Montserrat-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Light.ttf"; path = "../src/assets/fonts/Montserrat/Montserrat-Light.ttf"; sourceTree = "<group>"; };
		BEA8BCBCEDF24C58B92CD984 /* Montserrat-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-ExtraBold.ttf"; path = "../src/assets/fonts/Montserrat/Montserrat-ExtraBold.ttf"; sourceTree = "<group>"; };
		DF2D4A61AEF9503FEBD6B1B8 /* Pods-hawakCommonPods-hawak-staging.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-hawakCommonPods-hawak-staging.debug.xcconfig"; path = "Target Support Files/Pods-hawakCommonPods-hawak-staging/Pods-hawakCommonPods-hawak-staging.debug.xcconfig"; sourceTree = "<group>"; };
		E50D1EB12C900734003256D5 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		E50D1EB32C900734003256D5 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		E50D1EB52C900734003256D5 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		E52285722C8EE7D1003A4D4B /* hawak-development.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "hawak-development.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		E52285732C8EE7D1003A4D4B /* Development-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Development-Info.plist"; sourceTree = "<group>"; };
		E52285862C8EE9F9003A4D4B /* hawak-staging.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "hawak-staging.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		E52285872C8EE9F9003A4D4B /* Staging-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Staging-Info.plist"; sourceTree = "<group>"; };
		E52285892C8EEAF8003A4D4B /* Config.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; path = Config.xcconfig; sourceTree = "<group>"; };
		E5419A872C94001B0058655D /* hawak.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = hawak.entitlements; path = hawak/hawak.entitlements; sourceTree = "<group>"; };
		E5419A882C94006F0058655D /* hawak-development.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "hawak-development.entitlements"; sourceTree = "<group>"; };
		E5419A892C94009A0058655D /* hawak-staging.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "hawak-staging.entitlements"; sourceTree = "<group>"; };
		E5568755FC1745EE8874395F /* Almarai-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Almarai-Light.ttf"; path = "../src/assets/fonts/Almarai/Almarai-Light.ttf"; sourceTree = "<group>"; };
		E58569F02CABCA8E00E36F09 /* scanner_beep.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = scanner_beep.mp3; sourceTree = "<group>"; };
		E5D0399B2D54A35A00949981 /* hawak-stagingRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "hawak-stagingRelease.entitlements"; sourceTree = "<group>"; };
		E5D48FB32C92C2AF008ED676 /* payment_Success.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; path = payment_Success.m4a; sourceTree = "<group>"; };
		E5E40CCD2C92B0D700E680DF /* hawak-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "hawak-Bridging-Header.h"; sourceTree = "<group>"; };
		E5E40CCE2C92B0D700E680DF /* hawak-development-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "hawak-development-Bridging-Header.h"; sourceTree = "<group>"; };
		E5E40CCF2C92B0D700E680DF /* hawak-staging-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "hawak-staging-Bridging-Header.h"; sourceTree = "<group>"; };
		E5E40CD02C92B0D800E680DF /* geolocation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = geolocation.swift; sourceTree = "<group>"; };
		E88E6C1896AC4F6E8D4CB04B /* Cairo-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Cairo-Light.ttf"; path = "../src/assets/fonts/Cairo/Cairo-Light.ttf"; sourceTree = "<group>"; };
		EAD92EB75F584F22B3730E84 /* Almarai-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Almarai-Regular.ttf"; path = "../src/assets/fonts/Almarai/Almarai-Regular.ttf"; sourceTree = "<group>"; };
		EB083238899647EFAB2C06F4 /* Almarai-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Almarai-Bold.ttf"; path = "../src/assets/fonts/Almarai/Almarai-Bold.ttf"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F14AF13B828111823D3DE22A /* Pods-hawakCommonPods-hawak.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-hawakCommonPods-hawak.debug.xcconfig"; path = "Target Support Files/Pods-hawakCommonPods-hawak/Pods-hawakCommonPods-hawak.debug.xcconfig"; sourceTree = "<group>"; };
		F575DA55B0EE49479DD35024 /* BootSplash.storyboard */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = BootSplash.storyboard; path = hawak/BootSplash.storyboard; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AFE5A802AF30F7BDAFBDC3B1 /* Pods_hawakCommonPods_hawakTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEF177BE2A7F40F7AC8F27FA /* Pods_hawakCommonPods_hawak.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E52285662C8EE7D1003A4D4B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FE07221F4A1AAE4A048E5D22 /* Pods_hawakCommonPods_hawak_development.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E522857A2C8EE9F9003A4D4B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				55B0B0675C9EDDFF3AC355F3 /* Pods_hawakCommonPods_hawak_staging.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* hawakTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* hawakTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = hawakTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* hawak */ = {
			isa = PBXGroup;
			children = (
				E5419A872C94001B0058655D /* hawak.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				14D1D27A9A375C0FA0FC6356 /* PrivacyInfo.xcprivacy */,
				F575DA55B0EE49479DD35024 /* BootSplash.storyboard */,
				612D0E3D3FA34BD59C87125E /* Colors.xcassets */,
			);
			name = hawak;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				A1B4FEA7FE89134B33432A86 /* Pods_hawakCommonPods_hawak.framework */,
				4F540CEDE1160BDB6260E2E6 /* Pods_hawakCommonPods_hawak_development.framework */,
				A21597C28E8D755CD2F53B61 /* Pods_hawakCommonPods_hawak_staging.framework */,
				3EF50B0E82DAA06D1FCC45B9 /* Pods_hawakCommonPods_hawakTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7213D7D8848F44ADA026037D /* Resources */ = {
			isa = PBXGroup;
			children = (
				EB083238899647EFAB2C06F4 /* Almarai-Bold.ttf */,
				1F0AC1AF61DB4B9C882284FA /* Almarai-ExtraBold.ttf */,
				E5568755FC1745EE8874395F /* Almarai-Light.ttf */,
				EAD92EB75F584F22B3730E84 /* Almarai-Regular.ttf */,
				0F878B5DEA0142A0B96F4AB7 /* Cairo-Bold.ttf */,
				341DB18F60234AE9B1AD32AA /* Cairo-ExtraBold.ttf */,
				E88E6C1896AC4F6E8D4CB04B /* Cairo-Light.ttf */,
				14950E11C7B346E3A287D756 /* Cairo-Medium.ttf */,
				AD358BC881214B18B8BA53A8 /* Cairo-Regular.ttf */,
				59C0F7035730474BB3298427 /* Cairo-SemiBold.ttf */,
				073316EDB5354E349C82D29B /* Montserrat-Bold.ttf */,
				BEA8BCBCEDF24C58B92CD984 /* Montserrat-ExtraBold.ttf */,
				AE794EE0731B4816A4ACCC1E /* Montserrat-Light.ttf */,
				AB049AB5240346A490DEB7D8 /* Montserrat-Medium.ttf */,
				3FAE459DAB60492C95E0B1C3 /* Montserrat-Regular.ttf */,
				83F8F914BF3F43F0854D6939 /* Montserrat-SemiBold.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				E5D0399B2D54A35A00949981 /* hawak-stagingRelease.entitlements */,
				E58569F02CABCA8E00E36F09 /* scanner_beep.mp3 */,
				E5419A892C94009A0058655D /* hawak-staging.entitlements */,
				E5419A882C94006F0058655D /* hawak-development.entitlements */,
				E5D48FB32C92C2AF008ED676 /* payment_Success.m4a */,
				E5E40CD02C92B0D800E680DF /* geolocation.swift */,
				E50D1EAF2C900734003256D5 /* GoogleServices */,
				E52285892C8EEAF8003A4D4B /* Config.xcconfig */,
				13B07FAE1A68108700A75B9A /* hawak */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* hawakTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				E52285732C8EE7D1003A4D4B /* Development-Info.plist */,
				E52285872C8EE9F9003A4D4B /* Staging-Info.plist */,
				E5E40CCD2C92B0D700E680DF /* hawak-Bridging-Header.h */,
				E5E40CCE2C92B0D700E680DF /* hawak-development-Bridging-Header.h */,
				E5E40CCF2C92B0D700E680DF /* hawak-staging-Bridging-Header.h */,
				7213D7D8848F44ADA026037D /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* hawak.app */,
				00E356EE1AD99517003FC87E /* hawakTests.xctest */,
				E52285722C8EE7D1003A4D4B /* hawak-development.app */,
				E52285862C8EE9F9003A4D4B /* hawak-staging.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				F14AF13B828111823D3DE22A /* Pods-hawakCommonPods-hawak.debug.xcconfig */,
				86D5F686AC31B8C1B86D4856 /* Pods-hawakCommonPods-hawak.release.xcconfig */,
				3FFB094AF6F2A1D8A2705D51 /* Pods-hawakCommonPods-hawak-development.debug.xcconfig */,
				A183A7C13FF1EAB0D177D40E /* Pods-hawakCommonPods-hawak-development.release.xcconfig */,
				DF2D4A61AEF9503FEBD6B1B8 /* Pods-hawakCommonPods-hawak-staging.debug.xcconfig */,
				77230F6B47EEC6CE827FD693 /* Pods-hawakCommonPods-hawak-staging.release.xcconfig */,
				64EE896E649BFADAB098819B /* Pods-hawakCommonPods-hawakTests.debug.xcconfig */,
				33578BED5BCE38B352B9C511 /* Pods-hawakCommonPods-hawakTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		E50D1EAF2C900734003256D5 /* GoogleServices */ = {
			isa = PBXGroup;
			children = (
				E50D1EB02C900734003256D5 /* Staging */,
				E50D1EB22C900734003256D5 /* Development */,
				E50D1EB42C900734003256D5 /* Production */,
			);
			path = GoogleServices;
			sourceTree = "<group>";
		};
		E50D1EB02C900734003256D5 /* Staging */ = {
			isa = PBXGroup;
			children = (
				E50D1EB12C900734003256D5 /* GoogleService-Info.plist */,
			);
			path = Staging;
			sourceTree = "<group>";
		};
		E50D1EB22C900734003256D5 /* Development */ = {
			isa = PBXGroup;
			children = (
				E50D1EB32C900734003256D5 /* GoogleService-Info.plist */,
			);
			path = Development;
			sourceTree = "<group>";
		};
		E50D1EB42C900734003256D5 /* Production */ = {
			isa = PBXGroup;
			children = (
				E50D1EB52C900734003256D5 /* GoogleService-Info.plist */,
			);
			path = Production;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* hawakTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "hawakTests" */;
			buildPhases = (
				3A263D972E93C002AF9C66B1 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				3A571CA79A069AF46BC5228F /* [CP] Embed Pods Frameworks */,
				AA7691A5F43D00876E5C4071 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = hawakTests;
			productName = hawakTests;
			productReference = 00E356EE1AD99517003FC87E /* hawakTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* hawak */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "hawak" */;
			buildPhases = (
				D822A8C511DB329691B8716F /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				8DE529FD06904C4DB1CD262B /* Upload Debug Symbols to Sentry */,
				48E06A5D3AEC05B8987F85BD /* [CP] Embed Pods Frameworks */,
				4F364F9F1D4B72D9F45CF608 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = hawak;
			productName = hawak;
			productReference = 13B07F961A680F5B00A75B9A /* hawak.app */;
			productType = "com.apple.product-type.application";
		};
		E52285612C8EE7D1003A4D4B /* hawak-development */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E522856F2C8EE7D1003A4D4B /* Build configuration list for PBXNativeTarget "hawak-development" */;
			buildPhases = (
				3C5A07BDAE02F545E35ED8DF /* [CP] Check Pods Manifest.lock */,
				E52285632C8EE7D1003A4D4B /* Sources */,
				E52285662C8EE7D1003A4D4B /* Frameworks */,
				E52285682C8EE7D1003A4D4B /* Resources */,
				E522856C2C8EE7D1003A4D4B /* Bundle React Native code and images */,
				F4B87F7A8025411B3CB06E7C /* [CP] Embed Pods Frameworks */,
				B39E406C1D4936E0A1DE530C /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "hawak-development";
			productName = hawak;
			productReference = E52285722C8EE7D1003A4D4B /* hawak-development.app */;
			productType = "com.apple.product-type.application";
		};
		E52285752C8EE9F9003A4D4B /* hawak-staging */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E52285832C8EE9F9003A4D4B /* Build configuration list for PBXNativeTarget "hawak-staging" */;
			buildPhases = (
				E09BA15D504F04DA933C6385 /* [CP] Check Pods Manifest.lock */,
				E52285772C8EE9F9003A4D4B /* Sources */,
				E522857A2C8EE9F9003A4D4B /* Frameworks */,
				E522857C2C8EE9F9003A4D4B /* Resources */,
				E52285802C8EE9F9003A4D4B /* Bundle React Native code and images */,
				A1778880DC7E42D29B5693DE /* [CP] Embed Pods Frameworks */,
				11F09C43189613A542BFAE73 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "hawak-staging";
			productName = hawak;
			productReference = E52285862C8EE9F9003A4D4B /* hawak-staging.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1520;
					};
					E52285612C8EE7D1003A4D4B = {
						LastSwiftMigration = 1520;
					};
					E52285752C8EE9F9003A4D4B = {
						LastSwiftMigration = 1520;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "hawak" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* hawak */,
				00E356ED1AD99517003FC87E /* hawakTests */,
				E52285612C8EE7D1003A4D4B /* hawak-development */,
				E52285752C8EE9F9003A4D4B /* hawak-staging */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E522858A2C8EEAF8003A4D4B /* Config.xcconfig in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				E5D48FB42C92C2B0008ED676 /* payment_Success.m4a in Resources */,
				9AB857F2BE99DFF67D29A628 /* PrivacyInfo.xcprivacy in Resources */,
				E50D1EBC2C900734003256D5 /* GoogleService-Info.plist in Resources */,
				3D8BAC6BC7454529B2EBA875 /* Almarai-Bold.ttf in Resources */,
				981009573B984768AAB58544 /* Almarai-ExtraBold.ttf in Resources */,
				A6436E3E0A4E4407875CE6B2 /* Almarai-Light.ttf in Resources */,
				9D1DFD36B7234AE58E2CC464 /* Almarai-Regular.ttf in Resources */,
				17B40FE3B08D43B5BCBC4F09 /* Cairo-Bold.ttf in Resources */,
				37E9E6002F974C27A32FF814 /* Cairo-ExtraBold.ttf in Resources */,
				5380B24B50B14C10800FF8DC /* Cairo-Light.ttf in Resources */,
				C1CDE67576A94BCB8C6D8956 /* Cairo-Medium.ttf in Resources */,
				1C880F1A960344A8A3075C2E /* Cairo-Regular.ttf in Resources */,
				C719CC1111A544718D51B634 /* Cairo-SemiBold.ttf in Resources */,
				0C6F484E6884447DB2CE30D5 /* Montserrat-Bold.ttf in Resources */,
				FF72BC7B78A9495AA979050B /* Montserrat-ExtraBold.ttf in Resources */,
				41C34EFCDEA44009A778C111 /* Montserrat-Light.ttf in Resources */,
				725525DB5FEC4D94ADC0FE2D /* Montserrat-Medium.ttf in Resources */,
				79F08C6523844AFCA2F85CC3 /* Montserrat-Regular.ttf in Resources */,
				47CA28E1A6414FF892C7A91F /* Montserrat-SemiBold.ttf in Resources */,
				E58569F12CABCA8E00E36F09 /* scanner_beep.mp3 in Resources */,
				39311FB3312549FCBD775CDF /* BootSplash.storyboard in Resources */,
				7260FC4312EE4779842A26D4 /* Colors.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E52285682C8EE7D1003A4D4B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E52285742C8EE831003A4D4B /* Development-Info.plist in Resources */,
				E58569F22CABCA8E00E36F09 /* scanner_beep.mp3 in Resources */,
				E52285692C8EE7D1003A4D4B /* LaunchScreen.storyboard in Resources */,
				E5D9DE802CCF8254003E813D /* BootSplash.storyboard in Resources */,
				E522856A2C8EE7D1003A4D4B /* Images.xcassets in Resources */,
				E5D48FB52C92C2B0008ED676 /* payment_Success.m4a in Resources */,
				E522856B2C8EE7D1003A4D4B /* PrivacyInfo.xcprivacy in Resources */,
				E50D1EBA2C900734003256D5 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E522857C2C8EE9F9003A4D4B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E522857D2C8EE9F9003A4D4B /* LaunchScreen.storyboard in Resources */,
				E58569F32CABCA8E00E36F09 /* scanner_beep.mp3 in Resources */,
				E522857E2C8EE9F9003A4D4B /* Images.xcassets in Resources */,
				E5D9DE812CCF8254003E813D /* BootSplash.storyboard in Resources */,
				E50D1EB82C900734003256D5 /* GoogleService-Info.plist in Resources */,
				E5D48FB62C92C2B0008ED676 /* payment_Success.m4a in Resources */,
				E522857F2C8EE9F9003A4D4B /* PrivacyInfo.xcprivacy in Resources */,
				E52285882C8EEA23003A4D4B /* Staging-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT \\\"/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode.sh $REACT_NATIVE_XCODE\\\"\"\n";
		};
		11F09C43189613A542BFAE73 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-staging/Pods-hawakCommonPods-hawak-staging-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-staging/Pods-hawakCommonPods-hawak-staging-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-staging/Pods-hawakCommonPods-hawak-staging-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3A263D972E93C002AF9C66B1 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-hawakCommonPods-hawakTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		3A571CA79A069AF46BC5228F /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawakTests/Pods-hawakCommonPods-hawakTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawakTests/Pods-hawakCommonPods-hawakTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawakTests/Pods-hawakCommonPods-hawakTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3C5A07BDAE02F545E35ED8DF /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-hawakCommonPods-hawak-development-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		48E06A5D3AEC05B8987F85BD /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak/Pods-hawakCommonPods-hawak-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak/Pods-hawakCommonPods-hawak-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak/Pods-hawakCommonPods-hawak-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4F364F9F1D4B72D9F45CF608 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak/Pods-hawakCommonPods-hawak-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak/Pods-hawakCommonPods-hawak-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak/Pods-hawakCommonPods-hawak-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8DE529FD06904C4DB1CD262B /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode-debug-files.sh";
		};
		A1778880DC7E42D29B5693DE /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-staging/Pods-hawakCommonPods-hawak-staging-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-staging/Pods-hawakCommonPods-hawak-staging-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-staging/Pods-hawakCommonPods-hawak-staging-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AA7691A5F43D00876E5C4071 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawakTests/Pods-hawakCommonPods-hawakTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawakTests/Pods-hawakCommonPods-hawakTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawakTests/Pods-hawakCommonPods-hawakTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B39E406C1D4936E0A1DE530C /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-development/Pods-hawakCommonPods-hawak-development-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-development/Pods-hawakCommonPods-hawak-development-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-development/Pods-hawakCommonPods-hawak-development-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D822A8C511DB329691B8716F /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-hawakCommonPods-hawak-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E09BA15D504F04DA933C6385 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-hawakCommonPods-hawak-staging-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E522856C2C8EE7D1003A4D4B /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		E52285802C8EE9F9003A4D4B /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		F4B87F7A8025411B3CB06E7C /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-development/Pods-hawakCommonPods-hawak-development-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-development/Pods-hawakCommonPods-hawak-development-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-hawakCommonPods-hawak-development/Pods-hawakCommonPods-hawak-development-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00E356F31AD99517003FC87E /* hawakTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				E5E40CD12C92B0D800E680DF /* geolocation.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E52285632C8EE7D1003A4D4B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E52285642C8EE7D1003A4D4B /* AppDelegate.mm in Sources */,
				E52285652C8EE7D1003A4D4B /* main.m in Sources */,
				E5E40CD22C92B0D800E680DF /* geolocation.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E52285772C8EE9F9003A4D4B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E52285782C8EE9F9003A4D4B /* AppDelegate.mm in Sources */,
				E52285792C8EE9F9003A4D4B /* main.m in Sources */,
				E5E40CD32C92B0D800E680DF /* geolocation.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* hawak */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 64EE896E649BFADAB098819B /* Pods-hawakCommonPods-hawakTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				DEVELOPMENT_TEAM = H689X6GUZY;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = hawakTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/hawak.app/hawak";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 33578BED5BCE38B352B9C511 /* Pods-hawakCommonPods-hawakTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = H689X6GUZY;
				INFOPLIST_FILE = hawakTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.hawak;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/hawak.app/hawak";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F14AF13B828111823D3DE22A /* Pods-hawakCommonPods-hawak.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = hawak/hawak.entitlements;
				CURRENT_PROJECT_VERSION = 9;
				DEVELOPMENT_TEAM = H689X6GUZY;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = hawak/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.3.2;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.hawak;
				PRODUCT_NAME = hawak;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "hawak-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 86D5F686AC31B8C1B86D4856 /* Pods-hawakCommonPods-hawak.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = hawak/hawak.entitlements;
				CURRENT_PROJECT_VERSION = 9;
				DEVELOPMENT_TEAM = H689X6GUZY;
				INFOPLIST_FILE = hawak/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.3.2;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.hawak;
				PRODUCT_NAME = hawak;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "hawak-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E52285892C8EEAF8003A4D4B /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E52285892C8EEAF8003A4D4B /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E52285702C8EE7D1003A4D4B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3FFB094AF6F2A1D8A2705D51 /* Pods-hawakCommonPods-hawak-development.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "hawak-development.entitlements";
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_TEAM = H689X6GUZY;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "Development-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.3.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.hawak;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "hawak-development-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		E52285712C8EE7D1003A4D4B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A183A7C13FF1EAB0D177D40E /* Pods-hawakCommonPods-hawak-development.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "hawak-development.entitlements";
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_TEAM = H689X6GUZY;
				INFOPLIST_FILE = "Development-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.3.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.hawak;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "hawak-development-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		E52285842C8EE9F9003A4D4B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DF2D4A61AEF9503FEBD6B1B8 /* Pods-hawakCommonPods-hawak-staging.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "hawak-staging.entitlements";
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_TEAM = H689X6GUZY;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "Staging-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.3.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.hawak;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "hawak-staging-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		E52285852C8EE9F9003A4D4B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 77230F6B47EEC6CE827FD693 /* Pods-hawakCommonPods-hawak-staging.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "hawak-stagingRelease.entitlements";
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_TEAM = H689X6GUZY;
				INFOPLIST_FILE = "Staging-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.3.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.hawak;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "hawak-staging-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "hawakTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "hawak" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "hawak" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E522856F2C8EE7D1003A4D4B /* Build configuration list for PBXNativeTarget "hawak-development" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E52285702C8EE7D1003A4D4B /* Debug */,
				E52285712C8EE7D1003A4D4B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E52285832C8EE9F9003A4D4B /* Build configuration list for PBXNativeTarget "hawak-staging" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E52285842C8EE9F9003A4D4B /* Debug */,
				E52285852C8EE9F9003A4D4B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
